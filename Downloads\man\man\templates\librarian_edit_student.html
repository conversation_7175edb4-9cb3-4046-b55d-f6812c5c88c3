{% extends "base.html" %}

{% block title %}Edit Student - Librarian{% endblock %}

{% block sidebar %}
{% include 'librarian_sidebar.html' %}
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h2><i class="fas fa-user-edit me-3 text-info"></i>Edit Student</h2>
        <p class="text-muted mb-0">Update student information and department details</p>
    </div>
    <div>
        <a href="{{ url_for('librarian_student_details', student_id=student.id) }}" class="btn btn-secondary">
            <i class="fas fa-arrow-left me-2"></i>Back to Details
        </a>
        <a href="{{ url_for('librarian_students') }}" class="btn btn-outline-secondary ms-2">
            <i class="fas fa-users me-2"></i>All Students
        </a>
    </div>
</div>

<!-- Student Edit Form -->
<div class="card autolib-card">
    <div class="card-header">
        <h5 class="mb-0"><i class="fas fa-edit me-2"></i>Student Information</h5>
    </div>
    <div class="card-body">
        <form method="POST" class="row g-3">
            <!-- Basic Information -->
            <div class="col-12">
                <h6 class="text-primary"><i class="fas fa-info-circle me-2"></i>Basic Information</h6>
                <hr>
            </div>
            
            <div class="col-md-6">
                <label for="user_id" class="form-label">User ID <span class="text-danger">*</span></label>
                <input type="text" class="form-control" id="user_id" name="user_id" value="{{ student.user_id }}" readonly>
                <div class="form-text">User ID cannot be changed</div>
            </div>
            
            <div class="col-md-6">
                <label for="name" class="form-label">Full Name <span class="text-danger">*</span></label>
                <input type="text" class="form-control" id="name" name="name" value="{{ student.name }}" required>
            </div>
            
            <div class="col-md-6">
                <label for="email" class="form-label">Email <span class="text-danger">*</span></label>
                <input type="email" class="form-control" id="email" name="email" value="{{ student.email }}" required>
            </div>
            
            <div class="col-md-6">
                <label for="roll_number" class="form-label">Roll Number</label>
                <input type="text" class="form-control" id="roll_number" name="roll_number" value="{{ student.roll_number }}" readonly>
                <div class="form-text">Roll number cannot be changed</div>
            </div>

            <!-- Academic Information -->
            <div class="col-12 mt-4">
                <h6 class="text-primary"><i class="fas fa-graduation-cap me-2"></i>Academic Information</h6>
                <hr>
            </div>
            
            <div class="col-md-6">
                <label for="college" class="form-label">College <span class="text-danger">*</span></label>
                <select class="form-select" id="college" name="college" required>
                    <option value="">Select College</option>
                    {% for college in colleges %}
                    <option value="{{ college.id }}" {% if student.college_id == college.id %}selected{% endif %}>
                        {{ college.name }}
                    </option>
                    {% endfor %}
                </select>
            </div>
            
            <div class="col-md-6">
                <label for="department" class="form-label">Department <span class="text-danger">*</span></label>
                <select class="form-select" id="department" name="department" required>
                    <option value="">Select Department</option>
                    {% for dept in departments %}
                    <option value="{{ dept.code }}" {% if student.department == dept.code %}selected{% endif %}>
                        {{ dept.code }} - {{ dept.name }}
                    </option>
                    {% endfor %}
                </select>
            </div>
            
            <div class="col-md-6">
                <label for="course" class="form-label">Course <span class="text-danger">*</span></label>
                <input type="text" class="form-control" id="course" name="course" value="{{ student.course }}" required>
            </div>
            
            <div class="col-md-6">
                <label for="current_year" class="form-label">Current Year <span class="text-danger">*</span></label>
                <select class="form-select" id="current_year" name="current_year" required>
                    <option value="">Select Year</option>
                    {% for year in range(1, 5) %}
                    <option value="{{ year }}" {% if student.current_year == year %}selected{% endif %}>
                        {{ year }}{{ 'st' if year == 1 else 'nd' if year == 2 else 'rd' if year == 3 else 'th' }} Year
                    </option>
                    {% endfor %}
                </select>
            </div>

            <!-- Validity Information -->
            <div class="col-12 mt-4">
                <h6 class="text-primary"><i class="fas fa-calendar-check me-2"></i>Validity Information</h6>
                <hr>
            </div>
            
            <div class="col-md-6">
                <label for="validity_date" class="form-label">Validity Date <span class="text-danger">*</span></label>
                <input type="date" class="form-control" id="validity_date" name="validity_date" 
                       value="{{ student.validity_date.strftime('%Y-%m-%d') if student.validity_date else '' }}" required>
                <div class="form-text">Student access expires on this date</div>
            </div>
            
            <div class="col-md-6">
                <label for="designation" class="form-label">Designation</label>
                <input type="text" class="form-control" id="designation" name="designation" 
                       value="{{ student.designation or 'Student' }}" readonly>
                <div class="form-text">Designation cannot be changed by librarians</div>
            </div>
            
            <!-- Action Buttons -->
            <div class="col-12 text-center mt-4">
                <button type="submit" class="btn btn-primary btn-lg me-3">
                    <i class="fas fa-save me-2"></i>Update Student
                </button>
                <a href="{{ url_for('librarian_student_details', student_id=student.id) }}" class="btn btn-secondary btn-lg">
                    <i class="fas fa-times me-2"></i>Cancel
                </a>
            </div>
        </form>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Load departments when college is selected
    $('#college').change(function() {
        const collegeId = $(this).val();
        const departmentSelect = $('#department');

        if (collegeId) {
            // Enable department dropdown and show loading
            departmentSelect.prop('disabled', false);
            departmentSelect.html('<option value="">Loading departments...</option>');

            // Fetch departments for selected college
            $.ajax({
                url: `/api/departments/${collegeId}`,
                method: 'GET',
                success: function(departments) {
                    departmentSelect.html('<option value="">Select Department</option>');
                    departments.forEach(function(dept) {
                        departmentSelect.append(`<option value="${dept.code}">${dept.code} - ${dept.name}</option>`);
                    });
                },
                error: function() {
                    departmentSelect.html('<option value="">Error loading departments</option>');
                }
            });
        } else {
            // Disable department dropdown if no college selected
            departmentSelect.prop('disabled', true);
            departmentSelect.html('<option value="">Select College First</option>');
        }
    });

    // Auto-fill course based on department selection
    $('#department').change(function() {
        const department = $(this).val();
        const departmentText = $(this).find('option:selected').text();

        if (department && !$('#course').val()) {
            const courseName = departmentText.split(' - ')[1];
            $('#course').val('B.Tech ' + courseName);
        }
    });

    // Set minimum validity date to today
    const today = new Date().toISOString().split('T')[0];
    $('#validity_date').attr('min', today);
});
</script>
{% endblock %}
