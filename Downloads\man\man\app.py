# Corrected: removed invalid placeholder
# Place all route decorators and functions after app = Flask(__name__)
from flask import Flask, render_template, request, redirect, url_for, session, flash, jsonify, send_file
from flask_sqlalchemy import SQLAlchemy
from sqlalchemy import text
from werkzeug.security import generate_password_hash, check_password_hash
from werkzeug.utils import secure_filename
from datetime import datetime, timedelta, date
from io import BytesIO
import os
import pandas as pd
import re
import threading
import atexit

app = Flask(__name__)
app.config['SECRET_KEY'] = 'your-secret-key-here'
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///library.db'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
app.config['UPLOAD_FOLDER'] = 'uploads'
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB max file size

# Create uploads directory if it doesn't exist
if not os.path.exists(app.config['UPLOAD_FOLDER']):
    os.makedirs(app.config['UPLOAD_FOLDER'])

db = SQLAlchemy(app)

# Template context processor to make common variables available to all templates
@app.context_processor
def inject_template_vars():
    from datetime import datetime, date
    return {
        'now': datetime.now(),
        'today': date.today()
    }

# Database Models
class Admin(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=False)
    password = db.Column(db.String(200), nullable=False)

class Librarian(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=False)
    password = db.Column(db.String(200), nullable=False)

class College(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(200), unique=True, nullable=False)
    code = db.Column(db.String(10), unique=True, nullable=False)
    address = db.Column(db.Text, nullable=True)
    phone = db.Column(db.String(20), nullable=True)
    email = db.Column(db.String(120), nullable=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    is_active = db.Column(db.Boolean, default=True)

    # Relationship
    departments = db.relationship('Department', backref='college', lazy=True, cascade='all, delete-orphan')

class Department(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(200), nullable=False)
    code = db.Column(db.String(10), nullable=False)
    college_id = db.Column(db.Integer, db.ForeignKey('college.id'), nullable=False)
    head_of_department = db.Column(db.String(100), nullable=True)
    phone = db.Column(db.String(20), nullable=True)
    email = db.Column(db.String(120), nullable=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    is_active = db.Column(db.Boolean, default=True)

    # Unique constraint for code within a college
    __table_args__ = (db.UniqueConstraint('college_id', 'code', name='unique_dept_code_per_college'),)

class Student(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.String(50), unique=True, nullable=False)
    username = db.Column(db.String(50), unique=True, nullable=True)  # Made nullable, use user_id for login
    name = db.Column(db.String(100), nullable=False)
    roll_number = db.Column(db.String(50), unique=True, nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=False)
    password = db.Column(db.String(200), nullable=False)
    department_id = db.Column(db.Integer, db.ForeignKey('department.id'), nullable=True)  # New foreign key
    college_id = db.Column(db.Integer, db.ForeignKey('college.id'), nullable=True)  # New foreign key
    department = db.Column(db.String(10), nullable=True)  # Keep for backward compatibility
    college = db.Column(db.String(100), nullable=True)  # Keep for backward compatibility
    designation = db.Column(db.String(20), nullable=False, default='Student')  # Student or Staff
    course = db.Column(db.String(100), nullable=False)
    dob = db.Column(db.Date, nullable=False)
    current_year = db.Column(db.Integer, nullable=False)
    validity_date = db.Column(db.Date, nullable=False)
    created_at = db.Column(db.DateTime, nullable=False, default=datetime.utcnow)

class Book(db.Model):
    book_id = db.Column(db.Integer, primary_key=True)
    access_no = db.Column(db.String(50), unique=True, nullable=False)  # Access Number
    title = db.Column(db.String(200), nullable=False)
    author = db.Column(db.String(100), nullable=False)
    publisher = db.Column(db.String(150), nullable=False)
    subject = db.Column(db.String(100), nullable=False)
    department_id = db.Column(db.Integer, db.ForeignKey('department.id'), nullable=True)  # New foreign key
    college_id = db.Column(db.Integer, db.ForeignKey('college.id'), nullable=True)  # New foreign key
    department = db.Column(db.String(10), nullable=True)  # Keep for backward compatibility
    category = db.Column(db.String(100), nullable=False)
    location = db.Column(db.String(100), nullable=False)  # Location of the book
    copies = db.Column(db.Integer, nullable=False, default=1)  # Total copies
    quantity = db.Column(db.Integer, nullable=False, default=1)  # For backward compatibility
    available_count = db.Column(db.Integer, nullable=False, default=1)
    created_at = db.Column(db.DateTime, nullable=False, default=datetime.utcnow)

class EBook(db.Model):
    ebook_id = db.Column(db.Integer, primary_key=True)
    access_no = db.Column(db.String(50), unique=True, nullable=False)  # Access Number
    title = db.Column(db.String(200), nullable=False)
    author = db.Column(db.String(100), nullable=False)
    publisher = db.Column(db.String(150), nullable=False)
    subject = db.Column(db.String(100), nullable=False)
    department = db.Column(db.String(10), nullable=False)  # CSE, IT, ECE, etc.
    category = db.Column(db.String(100), nullable=False)
    file_format = db.Column(db.String(10), nullable=False)  # PDF, EPUB, etc.
    file_size = db.Column(db.String(20), nullable=True)  # File size in MB
    download_url = db.Column(db.String(500), nullable=True)  # URL or file path
    isbn = db.Column(db.String(20), nullable=True)  # ISBN number
    pages = db.Column(db.Integer, nullable=True)  # Number of pages
    language = db.Column(db.String(50), nullable=False, default='English')
    description = db.Column(db.Text, nullable=True)  # Book description
    is_active = db.Column(db.Boolean, nullable=False, default=True)  # Active status
    created_at = db.Column(db.DateTime, nullable=False, default=datetime.utcnow)

class Issue(db.Model):
    issue_id = db.Column(db.Integer, primary_key=True)
    book_id = db.Column(db.Integer, db.ForeignKey('book.book_id'), nullable=False)
    student_id = db.Column(db.Integer, db.ForeignKey('student.id'), nullable=False)
    issue_date = db.Column(db.DateTime, nullable=False, default=datetime.utcnow)
    due_date = db.Column(db.DateTime, nullable=False)
    return_date = db.Column(db.DateTime, nullable=True)
    fine = db.Column(db.Float, default=0.0)

    book = db.relationship('Book', backref=db.backref('issues', lazy=True))
    student = db.relationship('Student', backref=db.backref('issues', lazy=True))

class NewsClipping(db.Model):
    clipping_id = db.Column(db.Integer, primary_key=True)
    clipping_no = db.Column(db.String(50), nullable=False, unique=True)
    newspaper_name = db.Column(db.String(200), nullable=False)
    news_type = db.Column(db.String(100), nullable=False)
    date = db.Column(db.Date, nullable=False)
    pages = db.Column(db.String(50), nullable=False)  # e.g., "1-3", "5", "7-9"
    keywords = db.Column(db.Text, nullable=False)
    abstract = db.Column(db.Text, nullable=False)
    content = db.Column(db.Text, nullable=False)
    college_id = db.Column(db.Integer, db.ForeignKey('college.id'), nullable=False)
    department_id = db.Column(db.Integer, db.ForeignKey('department.id'), nullable=False)
    created_at = db.Column(db.DateTime, nullable=False, default=datetime.utcnow)
    created_by = db.Column(db.String(50), nullable=False)  # Admin who created it
    is_active = db.Column(db.Boolean, nullable=False, default=True)

    # Relationships
    college = db.relationship('College', backref=db.backref('news_clippings', lazy=True))
    department = db.relationship('Department', backref=db.backref('news_clippings', lazy=True))

class GateUser(db.Model):
    gate_user_id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(50), nullable=False, unique=True)
    password = db.Column(db.String(255), nullable=False)  # Hashed password
    role = db.Column(db.String(20), nullable=False, default='gate_operator')  # Always gate_operator
    is_active = db.Column(db.Boolean, nullable=False, default=True)
    created_at = db.Column(db.DateTime, nullable=False, default=datetime.utcnow)
    created_by = db.Column(db.String(50), nullable=False)  # Admin who created it
    last_login = db.Column(db.DateTime, nullable=True)

class GateEntry(db.Model):
    entry_id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.String(20), nullable=False)  # Student user_id from barcode
    student_id = db.Column(db.Integer, db.ForeignKey('student.id'), nullable=True)  # Link to student if found
    entry_time = db.Column(db.DateTime, nullable=False, default=datetime.utcnow)
    exit_time = db.Column(db.DateTime, nullable=True)
    entry_type = db.Column(db.String(10), nullable=False)  # 'IN' or 'OUT'
    gate_operator = db.Column(db.String(50), nullable=False)  # Who was operating the gate
    notes = db.Column(db.Text, nullable=True)
    is_valid_entry = db.Column(db.Boolean, nullable=False, default=True)  # If student found in DB
    created_at = db.Column(db.DateTime, nullable=False, default=datetime.utcnow)

    # Relationships
    student = db.relationship('Student', backref=db.backref('gate_entries', lazy=True))

class LibrarySettings(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    setting_name = db.Column(db.String(50), unique=True, nullable=False)
    setting_value = db.Column(db.String(200), nullable=False)
    description = db.Column(db.String(500))
    updated_by = db.Column(db.String(50))
    updated_at = db.Column(db.DateTime, default=datetime.utcnow)

# Enhanced Circulation Models
class Reservation(db.Model):
    reservation_id = db.Column(db.Integer, primary_key=True)
    book_id = db.Column(db.Integer, db.ForeignKey('book.book_id'), nullable=False)
    student_id = db.Column(db.Integer, db.ForeignKey('student.id'), nullable=False)
    reservation_date = db.Column(db.DateTime, default=datetime.utcnow)
    status = db.Column(db.String(20), default='Active')  # Active, Fulfilled, Cancelled
    notification_sent = db.Column(db.Boolean, default=False)
    expiry_date = db.Column(db.DateTime)
    priority = db.Column(db.Integer, default=1)  # Queue position

    book = db.relationship('Book', backref='reservations')
    student = db.relationship('Student', backref='reservations')

class Payment(db.Model):
    payment_id = db.Column(db.Integer, primary_key=True)
    student_id = db.Column(db.Integer, db.ForeignKey('student.id'), nullable=False)
    amount = db.Column(db.Float, nullable=False)
    payment_type = db.Column(db.String(50), nullable=False)  # Fine, Fee, Damage, Lost
    payment_method = db.Column(db.String(50), default='Cash')  # Cash, Online, Card
    description = db.Column(db.String(200))
    payment_date = db.Column(db.DateTime, default=datetime.utcnow)
    processed_by = db.Column(db.String(100))  # Staff member who processed
    receipt_number = db.Column(db.String(50), unique=True)
    issue_id = db.Column(db.Integer, db.ForeignKey('issue.issue_id'), nullable=True)  # Related issue if applicable

    student = db.relationship('Student', backref='payments')
    issue = db.relationship('Issue', backref='payments')

class BindingRecord(db.Model):
    binding_id = db.Column(db.Integer, primary_key=True)
    book_id = db.Column(db.Integer, db.ForeignKey('book.book_id'), nullable=False)
    vendor_name = db.Column(db.String(200), nullable=False)
    vendor_contact = db.Column(db.String(100))
    date_sent = db.Column(db.DateTime, default=datetime.utcnow)
    expected_return = db.Column(db.DateTime)
    actual_return = db.Column(db.DateTime)
    status = db.Column(db.String(50), default='Sent')  # Sent, Returned, Lost
    cost = db.Column(db.Float)
    notes = db.Column(db.Text)
    processed_by = db.Column(db.String(100))

    book = db.relationship('Book', backref='binding_records')

# Helper Functions
def migrate_database():
    """Migrate database to add new columns to existing Student and Book tables"""
    with app.app_context():
        try:
            # Migrate Student table
            try:
                db.session.execute(text('SELECT user_id FROM student LIMIT 1'))
                print("✅ Student table already has new columns")
            except:
                print("🔄 Adding new columns to Student table...")
                
                # Add missing columns to existing Student table
                db.session.execute(text('ALTER TABLE student ADD COLUMN user_id VARCHAR(50)'))
                db.session.execute(text('ALTER TABLE student ADD COLUMN username VARCHAR(50)'))
                db.session.execute(text('ALTER TABLE student ADD COLUMN department VARCHAR(10)'))
                db.session.execute(text('ALTER TABLE student ADD COLUMN college VARCHAR(100) DEFAULT "Engineering College"'))
                db.session.execute(text('ALTER TABLE student ADD COLUMN designation VARCHAR(20) DEFAULT "Student"'))
                db.session.execute(text('ALTER TABLE student ADD COLUMN course VARCHAR(100)'))
                db.session.execute(text('ALTER TABLE student ADD COLUMN dob DATE'))
                db.session.execute(text('ALTER TABLE student ADD COLUMN current_year INTEGER'))
                db.session.execute(text('ALTER TABLE student ADD COLUMN validity_date DATE'))
                db.session.execute(text('ALTER TABLE student ADD COLUMN created_at DATETIME DEFAULT CURRENT_TIMESTAMP'))
                
                db.session.commit()
                
                # Update existing student records with default values
                from datetime import date, timedelta
                default_validity = date.today() + timedelta(days=365)  # 1 year from now
                
                students = Student.query.all()
                for student in students:
                    if not student.user_id:
                        student.user_id = f"STU{student.id:03d}"
                    if not student.username:
                        student.username = student.email.split('@')[0] if student.email else f"student{student.id}"
                    if not student.department:
                        student.department = 'CSE'
                    if not student.college:
                        student.college = 'Engineering College'
                    if not student.designation:
                        student.designation = 'Student'
                    if not student.course:
                        student.course = 'B.Tech Computer Science'
                    if not student.dob:
                        student.dob = date(2000, 1, 1)  # Default DOB
                    if not student.current_year:
                        student.current_year = 2
                    if not student.validity_date:
                        student.validity_date = default_validity
                
                db.session.commit()
                print("✅ Student table migration completed")

            # Migrate Book table
            try:
                db.session.execute(text('SELECT access_no FROM book LIMIT 1'))
                print("✅ Book table already has new columns")
            except:
                print("🔄 Adding new columns to Book table...")
                
                # Add missing columns to existing Book table
                db.session.execute(text('ALTER TABLE book ADD COLUMN access_no VARCHAR(50)'))
                db.session.execute(text('ALTER TABLE book ADD COLUMN publisher VARCHAR(150)'))
                db.session.execute(text('ALTER TABLE book ADD COLUMN subject VARCHAR(100)'))
                db.session.execute(text('ALTER TABLE book ADD COLUMN department VARCHAR(10)'))
                db.session.execute(text('ALTER TABLE book ADD COLUMN location VARCHAR(100)'))
                db.session.execute(text('ALTER TABLE book ADD COLUMN copies INTEGER DEFAULT 1'))
                db.session.execute(text('ALTER TABLE book ADD COLUMN created_at DATETIME DEFAULT CURRENT_TIMESTAMP'))
                
                db.session.commit()
                
                # Update existing book records with default values
                books = Book.query.all()
                for book in books:
                    if not book.access_no:
                        book.access_no = f"ACC{book.book_id:05d}"
                    if not book.publisher:
                        book.publisher = 'Unknown Publisher'
                    if not book.subject:
                        book.subject = book.category  # Use category as subject initially
                    if not book.department:
                        book.department = 'CSE'
                    if not book.location:
                        book.location = 'Section A, Shelf 1'
                    if not book.copies:
                        book.copies = book.quantity
                
                db.session.commit()
                print("✅ Book table migration completed")
                
            # Initialize Library Settings
            try:
                existing_settings = LibrarySettings.query.first()
                if not existing_settings:
                    print("🔄 Initializing library settings...")
                    
                    default_settings = [
                        {
                            'setting_name': 'student_book_limit',
                            'setting_value': '3',
                            'description': 'Maximum number of books a student can issue at once',
                            'updated_by': 'system'
                        },
                        {
                            'setting_name': 'staff_book_limit',
                            'setting_value': '5',
                            'description': 'Maximum number of books a staff member can issue at once',
                            'updated_by': 'system'
                        },
                        {
                            'setting_name': 'default_issue_days',
                            'setting_value': '14',
                            'description': 'Default number of days for book issue period',
                            'updated_by': 'system'
                        },
                        {
                            'setting_name': 'fine_per_day',
                            'setting_value': '2.0',
                            'description': 'Fine amount per day for overdue books (in rupees)',
                            'updated_by': 'system'
                        },
                        {
                            'setting_name': 'renewal_limit',
                            'setting_value': '2',
                            'description': 'Maximum number of times a book can be renewed',
                            'updated_by': 'system'
                        }
                    ]
                    
                    for setting_data in default_settings:
                        setting = LibrarySettings(**setting_data)
                        db.session.add(setting)
                    
                    db.session.commit()
                    print("✅ Library settings initialized")
                else:
                    print("✅ Library settings already exist")
                    
            except Exception as e:
                print(f"Settings initialization error: {str(e)}")
                db.session.rollback()
                
        except Exception as e:
            print(f"Migration error: {str(e)}")
            db.session.rollback()

def init_db():
    with app.app_context():
        # Only create tables if they don't exist (preserve existing data)
        print("🔍 Checking database...")
        db.create_all()

        # Only create default users if they don't exist
        existing_admin = Admin.query.first()
        existing_librarian = Librarian.query.first()

        if not existing_admin:
            print("👤 Creating default admin...")
            admin = Admin(
                name='System Administrator',
                email='<EMAIL>',
                password=generate_password_hash('admin123')
            )
            db.session.add(admin)

        if not existing_librarian:
            print("📚 Creating default librarian...")
            librarian = Librarian(
                name='Head Librarian',
                email='<EMAIL>',
                password=generate_password_hash('librarian123')
            )
            db.session.add(librarian)

        # Create default colleges and departments if they don't exist
        existing_college = College.query.first()
        if not existing_college:
            print("🏫 Creating default colleges and departments...")

            # Create default college
            college = College(
                name='Engineering College',
                code='ENG',
                address='Main Campus',
                phone='************',
                email='<EMAIL>'
            )
            db.session.add(college)
            db.session.flush()  # Get the college ID

            # Create default departments
            departments = [
                {'name': 'Computer Science Engineering', 'code': 'CSE'},
                {'name': 'Information Technology', 'code': 'IT'},
                {'name': 'Electronics and Communication Engineering', 'code': 'ECE'},
                {'name': 'Electrical Engineering', 'code': 'EEE'},
                {'name': 'Mechanical Engineering', 'code': 'MECH'},
                {'name': 'Civil Engineering', 'code': 'CIVIL'},
                {'name': 'Chemical Engineering', 'code': 'CHEM'},
                {'name': 'Biotechnology', 'code': 'BT'},
                {'name': 'Mathematics', 'code': 'MATH'},
                {'name': 'Physics', 'code': 'PHY'},
                {'name': 'Chemistry', 'code': 'CHEM'},
                {'name': 'English', 'code': 'ENG'},
            ]

            for dept_data in departments:
                department = Department(
                    name=dept_data['name'],
                    code=dept_data['code'],
                    college_id=college.id,
                    head_of_department='',
                    phone='',
                    email=''
                )
                db.session.add(department)

            print("✅ Default colleges and departments created")

        if not existing_admin or not existing_librarian or not existing_college:
            db.session.commit()
            print("✅ Database initialized with default data")
            if not existing_admin:
                print("👤 Admin: <EMAIL> / admin123")
            if not existing_librarian:
                print("📚 Librarian: <EMAIL> / librarian123")
            if not existing_college:
                print("🏫 Default college and departments created")
        else:
            print("✅ Database already initialized - preserving existing data")

def reset_db_for_development():
    """DEVELOPMENT ONLY: Reset database and clear all data"""
    with app.app_context():
        print("🧹 DEVELOPMENT: Cleaning database...")
        db.drop_all()
        db.create_all()

        # Create default admin
        admin = Admin(
            name='System Administrator',
            email='<EMAIL>',
            password=generate_password_hash('admin123')
        )
        db.session.add(admin)

        # Create default librarian
        librarian = Librarian(
            name='Head Librarian',
            email='<EMAIL>',
            password=generate_password_hash('librarian123')
        )
        db.session.add(librarian)

        db.session.commit()
        print("✅ Database reset for development")
        print("👤 Admin: <EMAIL> / admin123")
        print("📚 Librarian: <EMAIL> / librarian123")

def calculate_fine(due_date, return_date=None):
    if return_date is None:
        return_date = datetime.utcnow()

    if return_date > due_date:
        days_overdue = (return_date - due_date).days
        return days_overdue * 2.0  # $2 per day fine
    return 0.0

def get_next_access_number():
    """Get the next available access number"""
    # Get the last access number from the database
    last_book = Book.query.order_by(Book.access_no.desc()).first()

    if not last_book:
        return "1"

    # Extract numeric part from access number
    last_access = last_book.access_no

    # Try to extract number from various formats
    import re
    numbers = re.findall(r'\d+', last_access)

    if numbers:
        # Get the last (usually largest) number found
        last_number = int(numbers[-1])
        return str(last_number + 1)
    else:
        # If no numbers found, start from 1
        return "1"

def allowed_file(filename):
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in {'xlsx', 'xls'}

def generate_next_access_number(prefix='BK'):
    """Generate the next access number for books or ebooks"""
    try:
        if prefix == 'BK':
            # Get the highest access number for books
            latest_book = Book.query.filter(Book.access_no.like(f'{prefix}%')).order_by(Book.access_no.desc()).first()
        else:
            # Get the highest access number for ebooks
            latest_book = EBook.query.filter(EBook.access_no.like(f'{prefix}%')).order_by(EBook.access_no.desc()).first()

        if latest_book:
            # Extract the numeric part from the access number
            access_no = latest_book.access_no
            if access_no.startswith(prefix):
                try:
                    # Extract number after prefix
                    number_part = access_no[len(prefix):]
                    # Remove any non-numeric characters and get the number
                    import re
                    numbers = re.findall(r'\d+', number_part)
                    if numbers:
                        next_number = int(numbers[0]) + 1
                    else:
                        next_number = 1
                except (ValueError, IndexError):
                    next_number = 1
            else:
                next_number = 1
        else:
            next_number = 1

        # Format with leading zeros (e.g., BK001, BK002, etc.)
        return f"{prefix}{next_number:03d}"

    except Exception as e:
        # Fallback to a timestamp-based access number if there's any error
        import time
        return f"{prefix}{int(time.time()) % 10000:04d}"

def export_to_excel(data, filename, sheet_name='Report'):
    """Export data to Excel format"""
    try:
        import io
        from openpyxl import Workbook
        from openpyxl.styles import Font, PatternFill, Alignment

        wb = Workbook()
        ws = wb.active
        ws.title = sheet_name

        if isinstance(data, list) and len(data) > 0:
            # Get headers from first row
            headers = list(data[0].keys()) if isinstance(data[0], dict) else data[0]

            # Add headers with formatting
            for col, header in enumerate(headers, 1):
                cell = ws.cell(row=1, column=col, value=header)
                cell.font = Font(bold=True, color="FFFFFF")
                cell.fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
                cell.alignment = Alignment(horizontal="center")

            # Add data rows
            for row_idx, row_data in enumerate(data, 2):
                if isinstance(row_data, dict):
                    for col_idx, value in enumerate(row_data.values(), 1):
                        ws.cell(row=row_idx, column=col_idx, value=value)
                else:
                    for col_idx, value in enumerate(row_data, 1):
                        ws.cell(row=row_idx, column=col_idx, value=value)

        # Auto-adjust column widths
        for column in ws.columns:
            max_length = 0
            column_letter = column[0].column_letter
            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
            adjusted_width = min(max_length + 2, 50)
            ws.column_dimensions[column_letter].width = adjusted_width

        # Save to BytesIO
        output = io.BytesIO()
        wb.save(output)
        output.seek(0)

        return output

    except Exception as e:
        print(f"Error exporting to Excel: {str(e)}")
        return None

def export_to_pdf(data, filename, title='Report'):
    """Export data to PDF format"""
    try:
        import io
        from reportlab.lib.pagesizes import letter, A4
        from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer
        from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
        from reportlab.lib import colors
        from reportlab.lib.units import inch

        buffer = io.BytesIO()
        doc = SimpleDocTemplate(buffer, pagesize=A4)
        elements = []

        # Styles
        styles = getSampleStyleSheet()
        title_style = ParagraphStyle(
            'CustomTitle',
            parent=styles['Heading1'],
            fontSize=16,
            spaceAfter=30,
            alignment=1  # Center alignment
        )

        # Add title
        title_para = Paragraph(title, title_style)
        elements.append(title_para)
        elements.append(Spacer(1, 12))

        if isinstance(data, list) and len(data) > 0:
            # Prepare table data
            if isinstance(data[0], dict):
                headers = list(data[0].keys())
                table_data = [headers]
                for row in data:
                    table_data.append(list(row.values()))
            else:
                table_data = data

            # Create table
            table = Table(table_data)
            table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                ('FONTSIZE', (0, 0), (-1, 0), 12),
                ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
                ('GRID', (0, 0), (-1, -1), 1, colors.black)
            ]))

            elements.append(table)

        # Build PDF
        doc.build(elements)
        buffer.seek(0)

        return buffer

    except Exception as e:
        print(f"Error exporting to PDF: {str(e)}")
        return None

def generate_username_password(name, user_id, user_type):
    """Generate username and password based on name and user_id"""
    # Clean name: remove spaces, special characters, convert to lowercase
    clean_name = re.sub(r'[^a-zA-Z]', '', name.lower())
    
    # Generate username: first part of name + user_id
    username = f"{clean_name[:6]}{user_id}@{user_type}.library.com"
    
    # Generate password: user_id + name (as specified)
    password = f"{user_id}{clean_name}"
    
    return username, password

def cleanup_expired_users():
    """Remove users whose validity date has expired"""
    with app.app_context():
        try:
            today = date.today()
            expired_students = Student.query.filter(Student.validity_date < today).all()
            
            for student in expired_students:
                # First, handle any pending book issues
                pending_issues = Issue.query.filter_by(student_id=student.id, return_date=None).all()
                for issue in pending_issues:
                    # Mark books as returned and calculate final fine
                    issue.return_date = datetime.utcnow()
                    issue.fine = calculate_fine(issue.due_date, issue.return_date)
                    # Return book to available stock
                    book = Book.query.get(issue.book_id)
                    if book:
                        book.available_count += 1
                
                # Delete the expired student
                db.session.delete(student)
                print(f"Deleted expired user: {student.name} (ID: {student.user_id})")
            
            if expired_students:
                db.session.commit()
                print(f"Cleanup completed: {len(expired_students)} expired users removed")
            
        except Exception as e:
            print(f"Error during cleanup: {str(e)}")
            db.session.rollback()

def schedule_cleanup():
    """Schedule daily cleanup of expired users"""
    cleanup_expired_users()
    # Schedule next cleanup in 24 hours
    timer = threading.Timer(86400.0, schedule_cleanup)  # 86400 seconds = 24 hours
    timer.daemon = True
    timer.start()

# Helper functions for dynamic data
def get_colleges():
    """Get all active colleges"""
    return College.query.filter_by(is_active=True).all()

def get_departments(college_id=None):
    """Get all active departments, optionally filtered by college"""
    if college_id:
        return Department.query.filter_by(college_id=college_id, is_active=True).all()
    return Department.query.filter_by(is_active=True).all()

def get_departments_for_dropdown(college_id=None):
    """Get departments formatted for dropdown (code, name)"""
    departments = get_departments(college_id)
    return [(dept.code, dept.name) for dept in departments]

def get_colleges_for_dropdown():
    """Get colleges formatted for dropdown (id, name)"""
    colleges = get_colleges()
    return [(college.id, college.name) for college in colleges]

DESIGNATION_CHOICES = [
    ('Student', 'Student'),
    ('Staff', 'Staff')
]

# Helper function to get setting values
def get_setting_value(setting_name, default_value=None):
    """Get a setting value from the database"""
    try:
        setting = LibrarySettings.query.filter_by(setting_name=setting_name).first()
        return setting.setting_value if setting else default_value
    except:
        return default_value

def update_setting_value(setting_name, new_value, updated_by='admin'):
    """Update a setting value in the database"""
    try:
        setting = LibrarySettings.query.filter_by(setting_name=setting_name).first()
        if setting:
            setting.setting_value = new_value
            setting.updated_by = updated_by
            setting.updated_at = datetime.utcnow()
        else:
            setting = LibrarySettings(
                setting_name=setting_name,
                setting_value=new_value,
                updated_by=updated_by
            )
            db.session.add(setting)
        
        db.session.commit()
        return True
    except Exception as e:
        db.session.rollback()
        return False

# Routes
@app.route('/')
def index():
    return render_template('index.html')

@app.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        username = request.form['username']
        password = request.form['password']

        user = None
        user_role = None

        # Auto-detect user role by checking all user types
        # Check Admin first (Admin only has email, no username)
        admin = Admin.query.filter_by(email=username).first()
        if admin and check_password_hash(admin.password, password):
            user = admin
            user_role = 'admin'

        # Check Librarian if not admin (Librarian only has email, no username)
        if not user:
            librarian = Librarian.query.filter_by(email=username).first()
            if librarian and check_password_hash(librarian.password, password):
                user = librarian
                user_role = 'librarian'

        # Check Student if not admin or librarian
        if not user:
            student = Student.query.filter(
                db.or_(
                    Student.email == username,
                    Student.username == username,
                    Student.user_id == username
                )
            ).first()

            if student and check_password_hash(student.password, password):
                # Check if student account is still valid
                if student.validity_date < date.today():
                    flash('Your account has expired. Please contact the administrator.')
                    return render_template('index.html')
                user = student
                user_role = 'student'

        if user and user_role:
            session['user_id'] = user.id
            session['user_role'] = user_role
            session['user_name'] = user.name
            flash(f'Welcome, {user.name}!')
            return redirect(url_for(f'{user_role}_dashboard'))
        else:
            flash('Invalid username or password')

    return render_template('index.html')

# Keep the old route for backward compatibility
@app.route('/login/<role>', methods=['GET', 'POST'])
def login_role(role):
    return redirect(url_for('login'))

@app.route('/logout')
def logout():
    session.clear()
    return redirect(url_for('index'))

# Admin Routes
@app.route('/admin/dashboard')
def admin_dashboard():
    if session.get('user_role') != 'admin':
        return redirect(url_for('login', role='admin'))

    # Get real statistics from database (no dummy data)
    total_books = Book.query.count()
    total_ebooks = EBook.query.filter_by(is_active=True).count()
    total_students = Student.query.count()
    total_librarians = Librarian.query.count()
    issued_books = Issue.query.filter_by(return_date=None).count()

    # Calculate overdue books using timezone-aware datetime
    from datetime import datetime, timezone
    now = datetime.now(timezone.utc)
    overdue_books = Issue.query.filter(
        Issue.return_date == None,
        Issue.due_date < now
    ).count()

    # Calculate available books
    available_books = total_books - issued_books if total_books > 0 else 0

    # Calculate total fines collected (real data only)
    total_fines = db.session.query(db.func.sum(Issue.fine)).filter(
        Issue.fine > 0,
        Issue.return_date.isnot(None)
    ).scalar() or 0

    stats = {
        'total_books': total_books,
        'total_ebooks': total_ebooks,
        'total_students': total_students,
        'total_librarians': total_librarians,
        'issued_books': issued_books,
        'overdue_books': overdue_books,
        'available_books': available_books,
        'total_fines': round(total_fines, 2)
    }

    return render_template('admin_dashboard.html', stats=stats)

# Librarian Routes
@app.route('/librarian/dashboard')
def librarian_dashboard():
    if session.get('user_role') != 'librarian':
        return redirect(url_for('login', role='librarian'))

    # Get real statistics from database for librarian view
    total_books = Book.query.count()
    total_ebooks = EBook.query.filter_by(is_active=True).count()
    total_students = Student.query.count()
    issued_books = Issue.query.filter_by(return_date=None).count()

    # Calculate overdue books using timezone-aware datetime
    from datetime import datetime, timezone
    now = datetime.now(timezone.utc)
    overdue_books = Issue.query.filter(
        Issue.return_date == None,
        Issue.due_date < now
    ).count()

    # Calculate available books
    available_books = total_books - issued_books if total_books > 0 else 0

    # Calculate total fines collected (real data only)
    total_fines = db.session.query(db.func.sum(Issue.fine)).filter(
        Issue.fine > 0,
        Issue.return_date.isnot(None)
    ).scalar() or 0

    stats = {
        'total_books': total_books,
        'total_ebooks': total_ebooks,
        'total_students': total_students,
        'issued_books': issued_books,
        'overdue_books': overdue_books,
        'available_books': available_books,
        'total_fines': round(total_fines, 2)
    }

    return render_template('librarian_dashboard.html', stats=stats)

@app.route('/librarian/books')
def librarian_books():
    if session.get('user_role') != 'librarian':
        return redirect(url_for('login', role='librarian'))

    books = Book.query.all()
    return render_template('librarian_books.html', books=books)

@app.route('/librarian/ebooks')
def librarian_ebooks():
    if session.get('user_role') != 'librarian':
        return redirect(url_for('login', role='librarian'))

    ebooks = EBook.query.all()
    return render_template('librarian_ebooks.html', ebooks=ebooks)

@app.route('/librarian/students')
def librarian_students():
    if session.get('user_role') != 'librarian':
        return redirect(url_for('login', role='librarian'))

    students = Student.query.all()
    return render_template('librarian_students.html', students=students)

@app.route('/librarian/add_book', methods=['GET', 'POST'])
def librarian_add_book():
    if session.get('user_role') != 'librarian':
        return redirect(url_for('login', role='librarian'))

    if request.method == 'POST':
        try:
            # Auto-generate access number if not provided or use provided one
            access_no = request.form.get('access_no', '').strip()
            if not access_no:
                access_no = generate_next_access_number('BK')
            else:
                # Check if provided access number already exists
                if Book.query.filter_by(access_no=access_no).first():
                    flash('Access number already exists! Auto-generating a new one.')
                    access_no = generate_next_access_number('BK')

            copies = int(request.form['copies'])

            # Get college and department information
            college_id = request.form.get('college')
            department_code = request.form.get('department')

            # Find department by code and college
            department = None
            if college_id and department_code:
                department = Department.query.filter_by(
                    college_id=college_id,
                    code=department_code,
                    is_active=True
                ).first()

            book = Book(
                access_no=access_no,
                title=request.form['title'],
                author=request.form['author'],
                publisher=request.form['publisher'],
                subject=request.form['subject'],
                college_id=int(college_id) if college_id else None,
                department_id=department.id if department else None,
                department=department_code,  # Keep for backward compatibility
                category=request.form['category'],
                location=request.form['location'],
                copies=copies,
                quantity=copies,  # For backward compatibility
                available_count=copies
            )
            db.session.add(book)
            db.session.commit()
            flash('Book added successfully!')
            return redirect(url_for('librarian_books'))

        except ValueError as e:
            flash('Invalid number of copies. Please enter a valid number.')
            return render_template('librarian_add_book.html', departments=get_departments_for_dropdown(), colleges=get_colleges_for_dropdown())
        except Exception as e:
            flash(f'Error adding book: {str(e)}')
            return render_template('librarian_add_book.html', departments=get_departments_for_dropdown(), colleges=get_colleges_for_dropdown())

    return render_template('librarian_add_book.html', departments=get_departments_for_dropdown(), colleges=get_colleges_for_dropdown())

@app.route('/librarian/bulk_books', methods=['GET', 'POST'])
def librarian_bulk_books():
    if session.get('user_role') != 'librarian':
        return redirect(url_for('login', role='librarian'))

    if request.method == 'POST':
        if 'file' not in request.files:
            flash('No file selected!')
            return redirect(request.url)

        file = request.files['file']
        if file.filename == '':
            flash('No file selected!')
            return redirect(request.url)

        if file and allowed_file(file.filename):
            try:
                # Read Excel file
                df = pd.read_excel(file)

                # Validate required columns (access_no is now optional)
                required_columns = ['title', 'author', 'publisher', 'subject', 'department', 'category', 'location', 'copies']
                missing_columns = [col for col in required_columns if col not in df.columns]

                if missing_columns:
                    flash(f'Missing required columns: {", ".join(missing_columns)}')
                    return redirect(request.url)

                success_count = 0
                error_count = 0
                errors = []

                for index, row in df.iterrows():
                    try:
                        # Auto-generate access number if not provided or if it already exists
                        access_no = str(row.get('access_no', '')).strip()
                        if not access_no or Book.query.filter_by(access_no=access_no).first():
                            access_no = generate_next_access_number('BK')
                            if str(row.get('access_no', '')).strip():
                                errors.append(f'Row {index + 2}: Access number {row["access_no"]} already exists, auto-generated {access_no}')

                        copies = int(row['copies']) if pd.notna(row['copies']) else 1

                        book = Book(
                            access_no=access_no,
                            title=str(row['title']),
                            author=str(row['author']),
                            publisher=str(row['publisher']),
                            subject=str(row['subject']),
                            department=str(row.get('department', 'CSE')),
                            category=str(row['category']),
                            location=str(row['location']),
                            copies=copies,
                            quantity=copies,
                            available_count=copies
                        )

                        db.session.add(book)
                        success_count += 1

                    except Exception as e:
                        errors.append(f'Row {index + 2}: {str(e)}')
                        error_count += 1

                if success_count > 0:
                    db.session.commit()
                    flash(f'Successfully added {success_count} books!')

                if error_count > 0:
                    flash(f'{error_count} books failed to add. Check errors below.', 'warning')
                    for error in errors[:10]:  # Show first 10 errors
                        flash(error, 'danger')

                return redirect(url_for('librarian_books'))

            except Exception as e:
                flash(f'Error processing file: {str(e)}')
                return redirect(request.url)
        else:
            flash('Invalid file format. Please upload an Excel file (.xlsx or .xls)')
            return redirect(request.url)

    return render_template('librarian_bulk_books.html')

@app.route('/librarian/add_ebook', methods=['GET', 'POST'])
def librarian_add_ebook():
    if session.get('user_role') != 'librarian':
        return redirect(url_for('login', role='librarian'))

    if request.method == 'POST':
        try:
            ebook = EBook(
                title=request.form['title'],
                author=request.form['author'],
                publisher=request.form['publisher'],
                subject=request.form['subject'],
                department=request.form.get('department', 'CSE'),
                category=request.form['category'],
                file_format=request.form.get('file_format', 'PDF'),
                file_size=request.form.get('file_size', '0'),
                download_url=request.form.get('download_url', ''),
                description=request.form.get('description', ''),
                is_active=True
            )
            db.session.add(ebook)
            db.session.commit()
            flash('E-Book added successfully!', 'success')
            return redirect(url_for('librarian_ebooks'))

        except Exception as e:
            flash(f'Error adding e-book: {str(e)}', 'danger')
            return render_template('librarian_add_ebook.html')

    return render_template('librarian_add_ebook.html')

@app.route('/librarian/bulk_ebooks', methods=['GET', 'POST'])
def librarian_bulk_ebooks():
    if session.get('user_role') != 'librarian':
        return redirect(url_for('login', role='librarian'))

    if request.method == 'POST':
        if 'file' not in request.files:
            flash('No file selected!')
            return redirect(request.url)

        file = request.files['file']
        if file.filename == '':
            flash('No file selected!')
            return redirect(request.url)

        if file and allowed_file(file.filename):
            try:
                # Read Excel file
                df = pd.read_excel(file)

                # Validate required columns
                required_columns = ['title', 'author', 'publisher', 'subject', 'department', 'category']
                missing_columns = [col for col in required_columns if col not in df.columns]

                if missing_columns:
                    flash(f'Missing required columns: {", ".join(missing_columns)}')
                    return redirect(request.url)

                success_count = 0
                error_count = 0
                errors = []

                for index, row in df.iterrows():
                    try:
                        ebook = EBook(
                            title=str(row['title']),
                            author=str(row['author']),
                            publisher=str(row['publisher']),
                            subject=str(row['subject']),
                            department=str(row.get('department', 'CSE')),
                            category=str(row['category']),
                            file_format=str(row.get('file_format', 'PDF')),
                            file_size=str(row.get('file_size', '0')),
                            download_url=str(row.get('download_url', '')),
                            description=str(row.get('description', '')),
                            is_active=True
                        )

                        db.session.add(ebook)
                        success_count += 1

                    except Exception as e:
                        errors.append(f'Row {index + 2}: {str(e)}')
                        error_count += 1

                if success_count > 0:
                    db.session.commit()
                    flash(f'Successfully added {success_count} e-books!')

                if error_count > 0:
                    flash(f'{error_count} e-books failed to add. Check errors below.', 'warning')
                    for error in errors[:10]:  # Show first 10 errors
                        flash(error, 'danger')

                return redirect(url_for('librarian_ebooks'))

            except Exception as e:
                flash(f'Error processing file: {str(e)}')
                return redirect(request.url)
        else:
            flash('Invalid file format. Please upload an Excel file (.xlsx or .xls)')
            return redirect(request.url)

    return render_template('librarian_bulk_ebooks.html')

@app.route('/librarian/edit_ebook/<int:ebook_id>', methods=['GET', 'POST'])
def librarian_edit_ebook(ebook_id):
    if session.get('user_role') != 'librarian':
        return redirect(url_for('login', role='librarian'))

    ebook = EBook.query.get_or_404(ebook_id)

    if request.method == 'POST':
        try:
            ebook.title = request.form['title']
            ebook.author = request.form['author']
            ebook.publisher = request.form['publisher']
            ebook.subject = request.form['subject']
            ebook.department = request.form.get('department', 'CSE')
            ebook.category = request.form['category']
            ebook.file_format = request.form.get('file_format', 'PDF')
            ebook.file_size = request.form.get('file_size', '0')
            ebook.download_url = request.form.get('download_url', '')
            ebook.description = request.form.get('description', '')
            ebook.is_active = 'is_active' in request.form

            db.session.commit()
            flash('E-Book updated successfully!', 'success')
            return redirect(url_for('librarian_ebooks'))

        except Exception as e:
            flash(f'Error updating e-book: {str(e)}', 'danger')

    return render_template('librarian_edit_ebook.html', ebook=ebook)

@app.route('/librarian/edit_book/<int:book_id>', methods=['GET', 'POST'])
def librarian_edit_book(book_id):
    if session.get('user_role') != 'librarian':
        return redirect(url_for('login', role='librarian'))
    book = Book.query.get_or_404(book_id)
    if request.method == 'POST':
        try:
            book.title = request.form['title']
            book.author = request.form['author']
            book.publisher = request.form['publisher']
            book.subject = request.form['subject']
            book.department = request.form['department']
            book.category = request.form['category']
            book.location = request.form['location']
            book.copies = int(request.form['copies'])
            db.session.commit()
            flash('Book updated successfully!', 'success')
            return redirect(url_for('librarian_books'))
        except Exception as e:
            flash(f'Error updating book: {str(e)}', 'danger')
    return render_template('librarian_edit_book.html', book=book)

@app.route('/librarian/books/delete/<int:book_id>', methods=['POST'])
def librarian_delete_book(book_id):
    if session.get('user_role') != 'librarian':
        return jsonify({'success': False, 'message': 'Unauthorized'}), 403

    try:
        book = Book.query.get_or_404(book_id)

        # Check if book is currently issued
        active_issues = Issue.query.filter_by(book_id=book_id, return_date=None).count()
        if active_issues > 0:
            return jsonify({'success': False, 'message': 'Cannot delete book. It is currently issued to students.'}), 400

        db.session.delete(book)
        db.session.commit()
        return jsonify({'success': True, 'message': 'Book deleted successfully!'})
    except Exception as e:
        return jsonify({'success': False, 'message': f'Error deleting book: {str(e)}'}), 500

@app.route('/librarian/student_search')
def librarian_student_search():
    if session.get('user_role') != 'librarian':
        return redirect(url_for('login', role='librarian'))
    q = request.args.get('q', '').strip()
    students = []
    if q:
        students = Student.query.filter(
            (Student.name.ilike(f'%{q}%')) |
            (Student.user_id.ilike(f'%{q}%')) |
            (Student.email.ilike(f'%{q}%'))
        ).all()
    return render_template('librarian_students.html', students=students)

@app.route('/librarian/student/<int:student_id>')
def librarian_student_details(student_id):
    if session.get('user_role') != 'librarian':
        return redirect(url_for('login', role='librarian'))
    student = Student.query.get_or_404(student_id)
    borrowed_books = Issue.query.filter_by(student_id=student_id, return_date=None).all()
    return render_template('librarian_student_details.html', student=student, borrowed_books=borrowed_books)

@app.route('/librarian/edit_student/<int:student_id>', methods=['GET', 'POST'])
def librarian_edit_student(student_id):
    if session.get('user_role') != 'librarian':
        return redirect(url_for('login', role='librarian'))

    student = Student.query.get_or_404(student_id)

    if request.method == 'POST':
        try:
            # Update student information
            student.name = request.form['name']
            student.email = request.form['email']
            student.user_id = request.form['user_id']
            student.department = request.form['department']
            student.phone = request.form.get('phone', '')
            student.address = request.form.get('address', '')
            student.year = int(request.form.get('year', 1))
            student.semester = int(request.form.get('semester', 1))

            # Check if user_id is unique (excluding current student)
            existing_student = Student.query.filter(
                Student.user_id == student.user_id,
                Student.student_id != student_id
            ).first()

            if existing_student:
                flash('User ID already exists for another student!', 'danger')
                return render_template('librarian_edit_student.html', student=student)

            # Check if email is unique (excluding current student)
            existing_email = Student.query.filter(
                Student.email == student.email,
                Student.student_id != student_id
            ).first()

            if existing_email:
                flash('Email already exists for another student!', 'danger')
                return render_template('librarian_edit_student.html', student=student)

            db.session.commit()
            flash('Student updated successfully!', 'success')
            return redirect(url_for('librarian_student_details', student_id=student_id))

        except Exception as e:
            flash(f'Error updating student: {str(e)}', 'danger')

    return render_template('librarian_edit_student.html', student=student)

@app.route('/librarian/download_book_template')
def librarian_download_book_template():
    if session.get('user_role') != 'librarian':
        return redirect(url_for('login', role='librarian'))

    # Create a sample Excel template for book upload
    import io
    from openpyxl import Workbook
    from openpyxl.styles import Font, PatternFill

    wb = Workbook()
    ws = wb.active
    ws.title = "Book Upload Template"

    # Headers
    headers = [
        'title', 'author', 'publisher', 'isbn', 'subject', 'department',
        'category', 'total_count', 'available_count', 'publication_year',
        'pages', 'language', 'location', 'price', 'description'
    ]

    # Add headers with formatting
    for col, header in enumerate(headers, 1):
        cell = ws.cell(row=1, column=col, value=header)
        cell.font = Font(bold=True)
        cell.fill = PatternFill(start_color="CCCCCC", end_color="CCCCCC", fill_type="solid")

    # Add sample data
    sample_data = [
        'Introduction to Computer Science', 'John Smith', 'Tech Publishers',
        '978-0123456789', 'Computer Science', 'CSE', 'textbook', 5, 5, 2024,
        450, 'English', 'A1-S1', 1200.00, 'Comprehensive introduction to CS concepts'
    ]

    for col, value in enumerate(sample_data, 1):
        ws.cell(row=2, column=col, value=value)

    # Auto-adjust column widths
    for column in ws.columns:
        max_length = 0
        column_letter = column[0].column_letter
        for cell in column:
            try:
                if len(str(cell.value)) > max_length:
                    max_length = len(str(cell.value))
            except:
                pass
        adjusted_width = min(max_length + 2, 50)
        ws.column_dimensions[column_letter].width = adjusted_width

    # Save to BytesIO
    output = io.BytesIO()
    wb.save(output)
    output.seek(0)

    return send_file(
        output,
        mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        as_attachment=True,
        download_name='book_upload_template.xlsx'
    )

@app.route('/librarian/bulk_users_template')
def librarian_bulk_users_template():
    if session.get('user_role') != 'librarian':
        return redirect(url_for('login', role='librarian'))

    # Create a sample Excel template for user upload
    import io
    from openpyxl import Workbook
    from openpyxl.styles import Font, PatternFill

    wb = Workbook()
    ws = wb.active
    ws.title = "User Upload Template"

    # Headers
    headers = [
        'user_id', 'name', 'email', 'department', 'user_type',
        'phone', 'address', 'year', 'semester'
    ]

    # Add headers with formatting
    for col, header in enumerate(headers, 1):
        cell = ws.cell(row=1, column=col, value=header)
        cell.font = Font(bold=True)
        cell.fill = PatternFill(start_color="CCCCCC", end_color="CCCCCC", fill_type="solid")

    # Add sample data for student
    sample_data = [
        'STU001', 'John Doe', '<EMAIL>', 'CSE', 'student',
        '9876543210', '123 Main St, City', 3, 1
    ]

    for col, value in enumerate(sample_data, 1):
        ws.cell(row=2, column=col, value=value)

    # Add sample data for librarian
    sample_data_lib = [
        'LIB001', 'Jane Smith', '<EMAIL>', 'Library', 'librarian',
        '9876543211', '456 Library St, City', '', ''
    ]

    for col, value in enumerate(sample_data_lib, 1):
        ws.cell(row=3, column=col, value=value)

    # Auto-adjust column widths
    for column in ws.columns:
        max_length = 0
        column_letter = column[0].column_letter
        for cell in column:
            try:
                if len(str(cell.value)) > max_length:
                    max_length = len(str(cell.value))
            except:
                pass
        adjusted_width = min(max_length + 2, 50)
        ws.column_dimensions[column_letter].width = adjusted_width

    # Save to BytesIO
    output = io.BytesIO()
    wb.save(output)
    output.seek(0)

    return send_file(
        output,
        mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        as_attachment=True,
        download_name='user_upload_template.xlsx'
    )

@app.route('/librarian/bulk_users', methods=['GET', 'POST'])
def librarian_bulk_users():
    if session.get('user_role') != 'librarian':
        return redirect(url_for('login', role='librarian'))

    if request.method == 'POST':
        if 'file' not in request.files:
            flash('No file selected!')
            return redirect(request.url)

        file = request.files['file']
        if file.filename == '':
            flash('No file selected!')
            return redirect(request.url)

        if file and allowed_file(file.filename):
            try:
                # Read Excel file
                df = pd.read_excel(file)

                # Validate required columns
                required_columns = ['user_id', 'name', 'email', 'department', 'user_type']
                missing_columns = [col for col in required_columns if col not in df.columns]

                if missing_columns:
                    flash(f'Missing required columns: {", ".join(missing_columns)}')
                    return redirect(request.url)

                success_count = 0
                error_count = 0
                errors = []

                for index, row in df.iterrows():
                    try:
                        user_type = str(row['user_type']).lower()

                        # Check if user already exists
                        if user_type == 'student':
                            # Check for existing student by user_id or email
                            existing_user = Student.query.filter(
                                (Student.user_id == str(row['user_id'])) |
                                (Student.email == str(row['email']))
                            ).first()
                            if existing_user:
                                errors.append(f'Row {index + 2}: Student with ID {row["user_id"]} or email {row["email"]} already exists')
                                error_count += 1
                                continue

                            # Generate roll number if not provided
                            roll_number = str(row.get('roll_number', row['user_id']))

                            # Check if roll number already exists
                            existing_roll = Student.query.filter_by(roll_number=roll_number).first()
                            if existing_roll:
                                roll_number = f"{roll_number}_{index}"  # Make it unique

                            # Generate password
                            password = str(row.get('password', f"{row['user_id']}123"))
                            hashed_password = generate_password_hash(password)

                            # Set default values for required fields
                            from datetime import date, timedelta
                            default_dob = row.get('dob', '2000-01-01')
                            if isinstance(default_dob, str):
                                default_dob = datetime.strptime(default_dob, '%Y-%m-%d').date()

                            validity_date = row.get('validity_date', date.today() + timedelta(days=365*4))  # 4 years
                            if isinstance(validity_date, str):
                                validity_date = datetime.strptime(validity_date, '%Y-%m-%d').date()

                            # Create new student
                            student = Student(
                                user_id=str(row['user_id']),
                                username=str(row.get('username', row['user_id'])),
                                name=str(row['name']),
                                roll_number=roll_number,
                                email=str(row['email']),
                                password=hashed_password,
                                department=str(row['department']),
                                college=str(row.get('college', 'Engineering College')),
                                designation=str(row.get('designation', 'Student')),
                                course=str(row.get('course', f"B.Tech {row['department']}")),
                                dob=default_dob,
                                current_year=int(row.get('current_year', 1)),
                                validity_date=validity_date
                            )
                            db.session.add(student)

                        elif user_type == 'librarian':
                            existing_user = Librarian.query.filter_by(email=row['email']).first()
                            if existing_user:
                                errors.append(f'Row {index + 2}: Librarian with email {row["email"]} already exists')
                                error_count += 1
                                continue

                            # Generate password for librarian
                            password = str(row.get('password', 'librarian123'))
                            hashed_password = generate_password_hash(password)

                            # Create new librarian
                            librarian = Librarian(
                                name=str(row['name']),
                                email=str(row['email']),
                                password=hashed_password
                            )
                            db.session.add(librarian)

                        else:
                            errors.append(f'Row {index + 2}: Invalid user type "{user_type}". Must be "student" or "librarian"')
                            error_count += 1
                            continue

                        success_count += 1

                    except Exception as e:
                        errors.append(f'Row {index + 2}: {str(e)}')
                        error_count += 1

                if success_count > 0:
                    db.session.commit()
                    flash(f'Successfully created {success_count} users!')

                if error_count > 0:
                    flash(f'{error_count} users failed to create. Check errors below.', 'warning')
                    for error in errors[:10]:  # Show first 10 errors
                        flash(error, 'danger')

                return redirect(url_for('librarian_students'))

            except Exception as e:
                flash(f'Error processing file: {str(e)}')
                return redirect(request.url)
        else:
            flash('Invalid file format. Please upload an Excel file (.xlsx or .xls)')
            return redirect(request.url)

    return render_template('librarian_bulk_users.html')

@app.route('/librarian/issue_return_dashboard')
def librarian_issue_return_dashboard():
    if session.get('user_role') != 'librarian':
        return redirect(url_for('login', role='librarian'))

    # Get recent issues and returns
    recent_issues = Issue.query.order_by(Issue.issue_date.desc()).limit(10).all()
    overdue_books = Issue.query.filter(
        Issue.return_date.is_(None),
        Issue.due_date < datetime.now().date()
    ).all()

    return render_template('librarian_issue_return_dashboard.html',
                         recent_issues=recent_issues,
                         overdue_books=overdue_books)

@app.route('/librarian/issue_return', methods=['GET', 'POST'])
def librarian_issue_return():
    if session.get('user_role') != 'librarian':
        return redirect(url_for('login', role='librarian'))

    if request.method == 'POST':
        action = request.form.get('action')

        if action == 'issue':
            try:
                # Get form data - support both ID-based and user-friendly inputs
                student_identifier = request.form.get('student_id') or request.form.get('user_id')
                book_identifier = request.form.get('book_id') or request.form.get('access_no')
                due_date_str = request.form.get('due_date')

                # Find student - try by ID first, then by user_id
                student = None
                if student_identifier.isdigit():
                    student = Student.query.get(int(student_identifier))
                if not student:
                    student = Student.query.filter_by(user_id=student_identifier).first()

                if not student:
                    flash(f'Student with ID {student_identifier} not found!', 'danger')
                    return redirect(request.url)

                # Find book - try by book_id first, then by access_no
                book = None
                if book_identifier.isdigit():
                    book = Book.query.get(int(book_identifier))
                if not book:
                    book = Book.query.filter_by(access_no=book_identifier).first()

                if not book:
                    flash(f'Book with identifier {book_identifier} not found!', 'danger')
                    return redirect(request.url)

                # Check if book is available
                if book.available_count <= 0:
                    flash(f'Book "{book.title}" is not available for issue!', 'danger')
                    return redirect(request.url)

                # Check for duplicate issue
                existing_issue = Issue.query.filter_by(
                    student_id=student.id,
                    book_id=book.book_id,
                    return_date=None
                ).first()
                if existing_issue:
                    flash(f'Student already has this book issued!', 'danger')
                    return redirect(request.url)

                # Parse due date
                if due_date_str:
                    try:
                        due_date = datetime.strptime(due_date_str, '%Y-%m-%d').date()
                    except ValueError:
                        flash('Invalid due date format!', 'danger')
                        return redirect(request.url)
                else:
                    due_date = datetime.now().date() + timedelta(days=14)

                # Create new issue
                issue = Issue(
                    student_id=student.id,
                    book_id=book.book_id,
                    issue_date=datetime.now().date(),
                    due_date=due_date
                )

                # Update book availability
                book.available_count -= 1

                db.session.add(issue)
                db.session.commit()

                flash(f'Book "{book.title}" issued to {student.name} successfully! Due date: {due_date.strftime("%Y-%m-%d")}', 'success')

            except Exception as e:
                flash(f'Error issuing book: {str(e)}', 'danger')
                db.session.rollback()

        elif action == 'return':
            try:
                issue_id = request.form['issue_id']
                issue = Issue.query.get(issue_id)

                if not issue or issue.return_date:
                    flash('Invalid issue or book already returned!', 'danger')
                    return redirect(request.url)

                # Update issue with return date
                issue.return_date = datetime.now().date()

                # Update book availability
                book = Book.query.get(issue.book_id)
                book.available_count += 1

                db.session.commit()

                flash(f'Book "{book.title}" returned successfully!', 'success')

            except Exception as e:
                flash(f'Error returning book: {str(e)}', 'danger')

    # Get data for the form
    students = Student.query.all()
    books = Book.query.filter(Book.available_count > 0).all()
    active_issues = Issue.query.filter_by(return_date=None).all()

    return render_template('librarian_issue_return.html',
                         students=students,
                         books=books,
                         active_issues=active_issues)

# Librarian API Routes
@app.route('/librarian/api/issue_book', methods=['POST'])
def librarian_api_issue_book():
    if session.get('user_role') != 'librarian':
        return jsonify({'success': False, 'message': 'Unauthorized'}), 403

    try:
        user_id = request.form.get('user_id', '').strip()
        access_no = request.form.get('access_no', '').strip()
        due_date_str = request.form.get('due_date', '').strip()

        # Validate required fields
        if not user_id:
            return jsonify({'success': False, 'message': 'Student ID is required'})
        if not access_no:
            return jsonify({'success': False, 'message': 'Book access number is required'})

        # Find student by user_id
        student = Student.query.filter_by(user_id=user_id).first()
        if not student:
            return jsonify({'success': False, 'message': f'Student with ID {user_id} not found'})

        # Find book by access number
        book = Book.query.filter_by(access_no=access_no).first()
        if not book:
            return jsonify({'success': False, 'message': f'Book with access number {access_no} not found'})

        # Check if book is available
        if book.available_count <= 0:
            return jsonify({'success': False, 'message': f'Book "{book.title}" is not available (all copies issued)'})

        # Check if student already has this book
        existing_issue = Issue.query.filter_by(
            student_id=student.id,
            book_id=book.book_id,
            return_date=None
        ).first()
        if existing_issue:
            return jsonify({'success': False, 'message': f'Student already has this book issued'})

        # Parse due date
        try:
            if due_date_str:
                due_date = datetime.strptime(due_date_str, '%Y-%m-%d').date()
            else:
                # Default to 14 days from now
                due_date = datetime.now().date() + timedelta(days=14)
        except ValueError:
            return jsonify({'success': False, 'message': 'Invalid due date format. Use YYYY-MM-DD'})

        # Validate due date is not in the past
        if due_date < datetime.now().date():
            return jsonify({'success': False, 'message': 'Due date cannot be in the past'})

        # Create new issue
        issue = Issue(
            student_id=student.id,
            book_id=book.book_id,
            issue_date=datetime.now().date(),
            due_date=due_date
        )

        # Update book availability
        book.available_count -= 1

        db.session.add(issue)
        db.session.commit()

        return jsonify({
            'success': True,
            'message': f'Book "{book.title}" issued to {student.name} successfully',
            'issue_details': {
                'student_name': student.name,
                'student_id': student.user_id,
                'book_title': book.title,
                'access_no': book.access_no,
                'issue_date': issue.issue_date.strftime('%Y-%m-%d'),
                'due_date': issue.due_date.strftime('%Y-%m-%d'),
                'remaining_copies': book.available_count
            }
        })

    except Exception as e:
        return jsonify({'success': False, 'message': f'Error: {str(e)}'})

@app.route('/librarian/api/return_book', methods=['POST'])
def librarian_api_return_book():
    if session.get('user_role') != 'librarian':
        return jsonify({'success': False, 'message': 'Unauthorized'}), 403

    try:
        issue_id = request.form.get('issue_id')
        issue = Issue.query.get(issue_id)

        if not issue or issue.return_date:
            return jsonify({'success': False, 'message': 'Invalid issue or book already returned'})

        # Update issue with return date
        issue.return_date = datetime.now().date()

        # Update book availability
        book = Book.query.get(issue.book_id)
        book.available_count += 1

        db.session.commit()

        return jsonify({'success': True, 'message': 'Book returned successfully'})

    except Exception as e:
        return jsonify({'success': False, 'message': f'Error: {str(e)}'})

@app.route('/librarian/api/search_user', methods=['POST'])
def librarian_api_search_user():
    if session.get('user_role') != 'librarian':
        return jsonify({'success': False, 'message': 'Unauthorized'}), 403

    try:
        user_id = request.form.get('user_id')
        student = Student.query.filter_by(user_id=user_id).first()

        if not student:
            return jsonify({'success': False, 'message': 'User not found'})

        # Get current issues
        current_issues = Issue.query.filter_by(student_id=student.student_id, return_date=None).all()

        user_data = {
            'name': student.name,
            'user_id': student.user_id,
            'email': student.email,
            'department': student.department,
            'current_issues': [{
                'issue_id': issue.issue_id,
                'book_title': issue.book.title,
                'issue_date': issue.issue_date.strftime('%Y-%m-%d'),
                'due_date': issue.due_date.strftime('%Y-%m-%d'),
                'is_overdue': issue.due_date < datetime.now().date()
            } for issue in current_issues]
        }

        return jsonify({'success': True, 'user': user_data})

    except Exception as e:
        return jsonify({'success': False, 'message': f'Error: {str(e)}'})

# Librarian Tools Routes
@app.route('/librarian/tools/barcode-generator')
def librarian_barcode_generator():
    if session.get('user_role') != 'librarian':
        return redirect(url_for('login', role='librarian'))
    return render_template('librarian_barcode_generator.html')

@app.route('/librarian/tools/label-printer')
def librarian_label_printer():
    if session.get('user_role') != 'librarian':
        return redirect(url_for('login', role='librarian'))
    return render_template('librarian_label_printer.html')

@app.route('/librarian/tools/inventory-check')
def librarian_inventory_check():
    if session.get('user_role') != 'librarian':
        return redirect(url_for('login', role='librarian'))
    return render_template('librarian_inventory_check.html')

@app.route('/librarian/tools/notifications')
def librarian_notifications():
    if session.get('user_role') != 'librarian':
        return redirect(url_for('login', role='librarian'))
    return render_template('librarian_notifications.html')

# Student Routes
@app.route('/student/dashboard')
def student_dashboard():
    if session.get('user_role') != 'student':
        return redirect(url_for('login'))

    # Get current student information
    student_id = session.get('user_id')
    student = Student.query.get(student_id)

    if not student:
        flash('Student not found. Please log in again.')
        return redirect(url_for('logout'))

    # Get student's current issues
    current_issues = Issue.query.filter_by(
        student_id=student_id,
        return_date=None
    ).all()

    # Get student's issue history
    issue_history = Issue.query.filter_by(student_id=student_id).order_by(Issue.issue_date.desc()).limit(10).all()

    # Calculate overdue books for this student
    from datetime import datetime, timezone
    now = datetime.now(timezone.utc)
    overdue_issues = [issue for issue in current_issues if issue.due_date < now]

    # Calculate total fines for this student
    total_fines = db.session.query(db.func.sum(Issue.fine)).filter(
        Issue.student_id == student_id,
        Issue.fine > 0
    ).scalar() or 0

    stats = {
        'current_issues': len(current_issues),
        'overdue_books': len(overdue_issues),
        'total_fines': round(total_fines, 2),
        'books_read': len(issue_history)
    }

    return render_template('student_dashboard.html',
                         student=student,
                         current_issues=current_issues,
                         issue_history=issue_history,
                         overdue_issues=overdue_issues,
                         stats=stats)

@app.route('/admin/settings', methods=['GET', 'POST'])
def admin_settings():
    if session.get('user_role') != 'admin':
        return redirect(url_for('login', role='admin'))
    
    if request.method == 'POST':
        # Update settings
        student_book_limit = request.form.get('student_book_limit')
        staff_book_limit = request.form.get('staff_book_limit')
        default_issue_days = request.form.get('default_issue_days')
        fine_per_day = request.form.get('fine_per_day')
        renewal_limit = request.form.get('renewal_limit')
        
        try:
            # Update each setting
            if student_book_limit:
                update_setting_value('student_book_limit', int(student_book_limit), session.get('username'))
            if staff_book_limit:
                update_setting_value('staff_book_limit', int(staff_book_limit), session.get('username'))
            if default_issue_days:
                update_setting_value('default_issue_days', int(default_issue_days), session.get('username'))
            if fine_per_day:
                update_setting_value('fine_per_day', float(fine_per_day), session.get('username'))
            if renewal_limit:
                update_setting_value('renewal_limit', int(renewal_limit), session.get('username'))
            
            flash('Settings updated successfully!', 'success')
        except ValueError as e:
            flash(f'Error updating settings: Invalid input values', 'danger')
        except Exception as e:
            flash(f'Error updating settings: {str(e)}', 'danger')
        
        return redirect(url_for('admin_settings'))
    
    # Get current settings
    settings = {
        'student_book_limit': get_setting_value('student_book_limit', 3),
        'staff_book_limit': get_setting_value('staff_book_limit', 5),
        'default_issue_days': get_setting_value('default_issue_days', 14),
        'fine_per_day': get_setting_value('fine_per_day', 2.0),
        'renewal_limit': get_setting_value('renewal_limit', 2)
    }
    
    return render_template('admin_settings.html', settings=settings)

@app.route('/admin/news-clippings')
def admin_news_clippings():
    if session.get('user_role') != 'admin':
        return redirect(url_for('login'))
    return render_template('admin_news_clippings.html',
                         colleges=get_colleges_for_dropdown(),
                         departments=get_departments_for_dropdown())

@app.route('/admin/news-clippings/add', methods=['GET', 'POST'])
def admin_add_news_clipping():
    if session.get('user_role') != 'admin':
        return redirect(url_for('login'))

    if request.method == 'POST':
        try:
            # Get form data
            clipping_no = request.form.get('clipping_no')
            newspaper_name = request.form.get('newspaper_name')
            news_type = request.form.get('news_type')
            date_str = request.form.get('date')
            pages = request.form.get('pages')
            keywords = request.form.get('keywords')
            abstract = request.form.get('abstract')
            content = request.form.get('content')
            college_id = request.form.get('college_id')
            department_id = request.form.get('department_id')

            # Validate required fields
            if not all([clipping_no, newspaper_name, news_type, date_str, pages, keywords, abstract, content, college_id, department_id]):
                flash('All fields are required!', 'error')
                return redirect(url_for('admin_add_news_clipping'))

            # Check if clipping number already exists
            existing_clipping = NewsClipping.query.filter_by(clipping_no=clipping_no).first()
            if existing_clipping:
                flash('News clipping number already exists!', 'error')
                return redirect(url_for('admin_add_news_clipping'))

            # Parse date
            from datetime import datetime
            date_obj = datetime.strptime(date_str, '%Y-%m-%d').date()

            # Create new news clipping
            news_clipping = NewsClipping(
                clipping_no=clipping_no,
                newspaper_name=newspaper_name,
                news_type=news_type,
                date=date_obj,
                pages=pages,
                keywords=keywords,
                abstract=abstract,
                content=content,
                college_id=int(college_id),
                department_id=int(department_id),
                created_by=session.get('user_id', 'admin')
            )

            db.session.add(news_clipping)
            db.session.commit()

            flash('News clipping added successfully!', 'success')
            return redirect(url_for('admin_news_clippings'))

        except Exception as e:
            db.session.rollback()
            flash(f'Error adding news clipping: {str(e)}', 'error')
            return redirect(url_for('admin_add_news_clipping'))

    return render_template('admin_add_news_clipping.html',
                         colleges=get_colleges_for_dropdown(),
                         departments=get_departments_for_dropdown())

@app.route('/api/admin/news-clippings')
def admin_news_clippings_api():
    if session.get('user_role') != 'admin':
        return jsonify({'error': 'Unauthorized'}), 401

    try:
        # Get query parameters for filtering
        college_id = request.args.get('college_id')
        department_id = request.args.get('department_id')
        news_type = request.args.get('news_type')
        start_date = request.args.get('start_date')
        end_date = request.args.get('end_date')

        # Build query
        query = NewsClipping.query.filter_by(is_active=True)

        if college_id:
            query = query.filter_by(college_id=college_id)
        if department_id:
            query = query.filter_by(department_id=department_id)
        if news_type:
            query = query.filter_by(news_type=news_type)
        if start_date:
            from datetime import datetime
            start_date_obj = datetime.strptime(start_date, '%Y-%m-%d').date()
            query = query.filter(NewsClipping.date >= start_date_obj)
        if end_date:
            from datetime import datetime
            end_date_obj = datetime.strptime(end_date, '%Y-%m-%d').date()
            query = query.filter(NewsClipping.date <= end_date_obj)

        # Get results
        clippings = query.order_by(NewsClipping.date.desc()).all()

        clippings_data = []
        for clipping in clippings:
            clippings_data.append({
                'clipping_id': clipping.clipping_id,
                'clipping_no': clipping.clipping_no,
                'newspaper_name': clipping.newspaper_name,
                'news_type': clipping.news_type,
                'date': clipping.date.strftime('%Y-%m-%d'),
                'pages': clipping.pages,
                'keywords': clipping.keywords,
                'abstract': clipping.abstract[:200] + '...' if len(clipping.abstract) > 200 else clipping.abstract,
                'college_name': clipping.college.name,
                'department_name': clipping.department.name,
                'created_by': clipping.created_by,
                'created_at': clipping.created_at.strftime('%Y-%m-%d %H:%M')
            })

        return jsonify({
            'clippings': clippings_data,
            'total_count': len(clippings_data)
        })

    except Exception as e:
        return jsonify({
            'clippings': [],
            'total_count': 0,
            'error': str(e)
        })

@app.route('/admin/books')
def admin_books():
    if session.get('user_role') != 'admin':
        return redirect(url_for('login', role='admin'))

    books = Book.query.all()
    return render_template('admin_books.html', books=books)

@app.route('/admin/add_book', methods=['GET', 'POST'])
def admin_add_book():
    if session.get('user_role') != 'admin':
        return redirect(url_for('login', role='admin'))
    
    if request.method == 'POST':
        try:
            # Auto-generate access number if not provided or use provided one
            access_no = request.form.get('access_no', '').strip()
            if not access_no:
                access_no = generate_next_access_number('BK')
            else:
                # Check if provided access number already exists
                if Book.query.filter_by(access_no=access_no).first():
                    flash('Access number already exists! Auto-generating a new one.')
                    access_no = generate_next_access_number('BK')

            copies = int(request.form['copies'])

            # Get college and department information
            college_id = request.form.get('college')
            department_code = request.form.get('department')

            # Find department by code and college
            department = None
            if college_id and department_code:
                department = Department.query.filter_by(
                    college_id=college_id,
                    code=department_code,
                    is_active=True
                ).first()

            book = Book(
                access_no=access_no,
                title=request.form['title'],
                author=request.form['author'],
                publisher=request.form['publisher'],
                subject=request.form['subject'],
                college_id=int(college_id) if college_id else None,
                department_id=department.id if department else None,
                department=department_code,  # Keep for backward compatibility
                category=request.form['category'],
                location=request.form['location'],
                copies=copies,
                quantity=copies,  # For backward compatibility
                available_count=copies
            )
            db.session.add(book)
            db.session.commit()
            flash('Book added successfully!')
            return redirect(url_for('admin_books'))
            
        except ValueError as e:
            flash('Invalid number of copies. Please enter a valid number.')
            return render_template('admin_add_book.html', departments=get_departments_for_dropdown(), colleges=get_colleges_for_dropdown())
        except Exception as e:
            flash(f'Error adding book: {str(e)}')
            return render_template('admin_add_book.html', departments=get_departments_for_dropdown(), colleges=get_colleges_for_dropdown())

    return render_template('admin_add_book.html', departments=get_departments_for_dropdown(), colleges=get_colleges_for_dropdown())

@app.route('/admin/edit_book/<int:book_id>', methods=['GET', 'POST'])
def admin_edit_book(book_id):
    if session.get('user_role') != 'admin':
        return redirect(url_for('login', role='admin'))
    
    book = Book.query.get_or_404(book_id)
    
    if request.method == 'POST':
        book.title = request.form['title']
        book.author = request.form['author']
        book.category = request.form['category']
        old_quantity = book.quantity
        new_quantity = int(request.form['quantity'])
        
        # Update available count based on quantity change
        difference = new_quantity - old_quantity
        book.quantity = new_quantity
        book.available_count = max(0, book.available_count + difference)
        
        db.session.commit()
        flash('Book updated successfully!')
        return redirect(url_for('admin_books'))
    
    return render_template('admin_edit_book.html', book=book)

@app.route('/admin/delete_book/<int:book_id>')
def admin_delete_book(book_id):
    if session.get('user_role') != 'admin':
        return redirect(url_for('login', role='admin'))
    
    book = Book.query.get_or_404(book_id)
    
    # Check if book is currently issued
    active_issues = Issue.query.filter_by(book_id=book_id, return_date=None).count()
    if active_issues > 0:
        flash('Cannot delete book. It is currently issued to students.')
        return redirect(url_for('admin_books'))
    
    db.session.delete(book)
    db.session.commit()
    flash('Book deleted successfully!')
    return redirect(url_for('admin_books'))

@app.route('/admin/librarians')
def admin_librarians():
    if session.get('user_role') != 'admin':
        return redirect(url_for('login', role='admin'))
    
    librarians = Librarian.query.all()
    return render_template('admin_librarians.html', librarians=librarians)

@app.route('/admin/add_librarian', methods=['GET', 'POST'])
def admin_add_librarian():
    if session.get('user_role') != 'admin':
        return redirect(url_for('login', role='admin'))
    
    if request.method == 'POST':
        # Check if email already exists
        if Librarian.query.filter_by(email=request.form['email']).first():
            flash('Email already exists!')
            return render_template('admin_add_librarian.html')
        
        librarian = Librarian(
            name=request.form['name'],
            email=request.form['email'],
            password=generate_password_hash(request.form['password'])
        )
        db.session.add(librarian)
        db.session.commit()
        flash('Librarian added successfully!')
        return redirect(url_for('admin_librarians'))
    
    return render_template('admin_add_librarian.html')

@app.route('/admin/students')
def admin_students():
    if session.get('user_role') != 'admin':
        return redirect(url_for('login', role='admin'))
    
    students = Student.query.all()
    return render_template('admin_students.html', students=students)

@app.route('/admin/student_details/<int:student_id>')
def admin_student_details(student_id):
    if session.get('user_role') != 'admin':
        return redirect(url_for('login', role='admin'))
    
    student = Student.query.get_or_404(student_id)
    active_issues = Issue.query.filter_by(student_id=student.id, return_date=None).all()
    issue_history = Issue.query.filter_by(student_id=student.id).order_by(Issue.issue_date.desc()).all()
    total_fine = sum(issue.fine for issue in issue_history if issue.fine)
    
    from datetime import datetime
    overdue_issues = [issue for issue in active_issues if issue.due_date and issue.due_date < datetime.utcnow()]
    
    return render_template('admin_student_details.html', 
                         student=student, 
                         active_issues=active_issues,
                         issue_history=issue_history,
                         total_fine=total_fine,
                         overdue_count=len(overdue_issues))

@app.route('/admin/add_student', methods=['GET', 'POST'])
def admin_add_student():
    if session.get('user_role') != 'admin':
        return redirect(url_for('login', role='admin'))
    
    if request.method == 'POST':
        try:
            # Check if user_id, username, email already exists
            if Student.query.filter_by(user_id=request.form['user_id']).first():
                flash('User ID already exists!', 'danger')
                return render_template('admin_add_student.html', departments=get_departments_for_dropdown(), colleges=get_colleges_for_dropdown(), designations=DESIGNATION_CHOICES)

            # Username check removed - using user_id for login

            if Student.query.filter_by(email=request.form['email']).first():
                flash('Email already exists!', 'danger')
                return render_template('admin_add_student.html', departments=get_departments_for_dropdown(), colleges=get_colleges_for_dropdown(), designations=DESIGNATION_CHOICES)
            
            # Parse validity date
            validity_date = None
            if request.form.get('validity_date'):
                from datetime import datetime
                validity_date = datetime.strptime(request.form['validity_date'], '%Y-%m-%d').date()
            
            # Parse date of birth
            dob = None
            if request.form.get('dob'):
                dob = datetime.strptime(request.form['dob'], '%Y-%m-%d').date()
            
            # Generate password: user_id + name
            password = request.form['user_id'] + request.form['name'].replace(' ', '').lower()
            hashed_password = generate_password_hash(password)
            
            # Get college and department information
            college_id = request.form.get('college')
            department_code = request.form.get('department')

            # Find department by code and college
            department = None
            if college_id and department_code:
                department = Department.query.filter_by(
                    college_id=college_id,
                    code=department_code,
                    is_active=True
                ).first()

            # Create new student (user_id serves as roll_number for students, staff_id for staff)
            student = Student(
                user_id=request.form['user_id'],
                name=request.form['name'],
                username=None,  # No longer using username - user_id is used for login
                email=request.form['email'],
                password=hashed_password,
                roll_number=request.form['user_id'],  # Use user_id as roll_number
                college_id=int(college_id) if college_id else None,
                department_id=department.id if department else None,
                department=department_code,  # Keep for backward compatibility
                college=College.query.get(college_id).name if college_id else None,  # Keep for backward compatibility
                designation=request.form['designation'],
                course=request.form.get('course'),
                current_year=request.form.get('current_year'),
                dob=dob,
                validity_date=validity_date
            )
            
            db.session.add(student)
            db.session.commit()
            
            flash(f'Student added successfully! Password: {password}', 'success')
            return redirect(url_for('admin_students'))
            
        except Exception as e:
            db.session.rollback()
            flash(f'Error adding student: {str(e)}', 'danger')
            
    return render_template('admin_add_student.html', departments=get_departments_for_dropdown(), colleges=get_colleges_for_dropdown(), designations=DESIGNATION_CHOICES)

@app.route('/admin/issue_history')
def admin_issue_history():
    if session.get('user_role') != 'admin':
        return redirect(url_for('login', role='admin'))

    issues = Issue.query.order_by(Issue.issue_date.desc()).all()

    from datetime import date
    overdue_count = len([i for i in issues if i.return_date is None and i.due_date and i.due_date.date() < date.today()])

    return render_template('admin_issue_history.html', issues=issues, overdue_count=overdue_count)

@app.route('/admin/circulation')
def admin_circulation():
    if session.get('user_role') != 'admin':
        return redirect(url_for('login', role='admin'))

    # Get circulation statistics
    from datetime import datetime, timedelta

    # Current circulation data
    total_issued = Issue.query.filter_by(return_date=None).count()
    total_overdue = Issue.query.filter(
        Issue.return_date == None,
        Issue.due_date < datetime.utcnow()
    ).count()

    # Recent activity (last 7 days)
    week_ago = datetime.utcnow() - timedelta(days=7)
    recent_issues = Issue.query.filter(Issue.issue_date >= week_ago).count()
    recent_returns = Issue.query.filter(
        Issue.return_date >= week_ago,
        Issue.return_date != None
    ).count()

    # Most popular books (by issue count)
    from sqlalchemy import func
    popular_books = db.session.query(
        Book.title,
        Book.author,
        Book.access_no,
        func.count(Issue.issue_id).label('issue_count')
    ).join(Issue).group_by(Book.book_id).order_by(func.count(Issue.issue_id).desc()).limit(10).all()

    # Active students with most books
    active_students = db.session.query(
        Student.name,
        Student.user_id,
        Student.department,
        func.count(Issue.issue_id).label('active_books')
    ).join(Issue).filter(Issue.return_date == None).group_by(Student.id).order_by(func.count(Issue.issue_id).desc()).limit(10).all()

    # Department-wise circulation
    dept_circulation = db.session.query(
        Student.department,
        func.count(Issue.issue_id).label('total_issues')
    ).join(Issue).filter(Issue.return_date == None).group_by(Student.department).all()

    circulation_data = {
        'total_issued': total_issued,
        'total_overdue': total_overdue,
        'recent_issues': recent_issues,
        'recent_returns': recent_returns,
        'popular_books': popular_books,
        'active_students': active_students,
        'dept_circulation': dept_circulation
    }

    return render_template('admin_circulation.html', data=circulation_data)

# Enhanced Circulation Routes
@app.route('/admin/circulation/counter')
def admin_circulation_counter():
    if session.get('user_role') != 'admin':
        return redirect(url_for('login'))
    return render_template('admin_circulation_counter.html')

@app.route('/librarian/circulation/counter')
def librarian_circulation_counter():
    if session.get('user_role') != 'librarian':
        return redirect(url_for('login'))
    return render_template('librarian_circulation_counter.html')

@app.route('/admin/circulation/bulk')
def admin_bulk_operations():
    if session.get('user_role') != 'admin':
        return redirect(url_for('login'))
    return render_template('admin_bulk_operations.html')

@app.route('/librarian/circulation/bulk')
def librarian_bulk_operations():
    if session.get('user_role') != 'librarian':
        return redirect(url_for('login'))
    return render_template('librarian_bulk_operations.html')

@app.route('/librarian/circulation/issue-history')
def librarian_issue_history():
    if session.get('user_role') != 'librarian':
        return redirect(url_for('login', role='librarian'))

    # Get all issues with related book and student information
    issues = db.session.query(Issue).join(Book).join(Student).all()

    # Add current datetime for template comparisons
    from datetime import datetime
    now = datetime.now()

    return render_template('librarian_issue_history.html', issues=issues, now=now)

@app.route('/admin/circulation/payments')
def admin_payment_management():
    if session.get('user_role') != 'admin':
        return redirect(url_for('login'))
    return render_template('admin_payment_management.html')

@app.route('/api/admin/quick-stats')
def admin_quick_stats():
    if session.get('user_role') != 'admin':
        return jsonify({'error': 'Unauthorized'}), 401

    from datetime import date
    today = date.today()

    # Calculate available books
    available_books = sum(book.available_count for book in Book.query.all())

    # Calculate books issued today
    issued_today = Issue.query.filter(
        db.func.date(Issue.issue_date) == today
    ).count()

    return jsonify({
        'available_books': available_books,
        'issued_today': issued_today
    })

@app.route('/api/librarian/quick-stats')
def librarian_quick_stats():
    if session.get('user_role') != 'librarian':
        return jsonify({'error': 'Unauthorized'}), 401

    from datetime import date
    today = date.today()

    # Calculate available books
    available_books = sum(book.available_count for book in Book.query.all())

    # Calculate books issued today
    issued_today = Issue.query.filter(
        db.func.date(Issue.issue_date) == today
    ).count()

    return jsonify({
        'available_books': available_books,
        'issued_today': issued_today
    })

@app.route('/api/admin/search-student')
def admin_search_student():
    if session.get('user_role') != 'admin':
        return jsonify({'error': 'Unauthorized'}), 401

    query = request.args.get('query', '').strip()
    if not query:
        return jsonify({'student': None})

    # Search by user_id or name
    student = Student.query.filter(
        db.or_(
            Student.user_id.ilike(f'%{query}%'),
            Student.name.ilike(f'%{query}%')
        )
    ).first()

    if student:
        active_books = Issue.query.filter_by(student_id=student.id, return_date=None).count()
        return jsonify({
            'student': {
                'id': student.id,
                'name': student.name,
                'user_id': student.user_id,
                'department': student.department,
                'active_books': active_books
            }
        })

    return jsonify({'student': None})

@app.route('/api/librarian/search-student')
def librarian_search_student():
    if session.get('user_role') != 'librarian':
        return jsonify({'error': 'Unauthorized'}), 401

    query = request.args.get('query', '').strip()
    if not query:
        return jsonify({'student': None})

    # Search by user_id or name
    student = Student.query.filter(
        db.or_(
            Student.user_id.ilike(f'%{query}%'),
            Student.name.ilike(f'%{query}%')
        )
    ).first()

    if student:
        active_books = Issue.query.filter_by(student_id=student.id, return_date=None).count()
        return jsonify({
            'student': {
                'id': student.id,
                'name': student.name,
                'user_id': student.user_id,
                'department': student.department,
                'active_books': active_books
            }
        })

    return jsonify({'student': None})

@app.route('/api/librarian/search-book')
def librarian_search_book():
    if session.get('user_role') != 'librarian':
        return jsonify({'error': 'Unauthorized'}), 401

    query = request.args.get('query', '').strip()
    if not query:
        return jsonify({'book': None})

    # Search by access_no or title
    book = Book.query.filter(
        db.or_(
            Book.access_no.ilike(f'%{query}%'),
            Book.title.ilike(f'%{query}%')
        )
    ).first()

    if book:
        return jsonify({
            'book': {
                'book_id': book.book_id,
                'access_no': book.access_no,
                'title': book.title,
                'author': book.author,
                'publisher': book.publisher,
                'subject': book.subject,
                'department': book.department,
                'location': book.location,
                'total_copies': book.copies,
                'available_count': book.available_count,
                'is_available': book.available_count > 0
            }
        })

    return jsonify({'book': None})

@app.route('/api/admin/outstanding-fines')
def admin_outstanding_fines():
    if session.get('user_role') != 'admin':
        return jsonify({'error': 'Unauthorized'}), 401

    # Get overdue issues to calculate fines
    from datetime import date, timedelta
    today = date.today()

    # Get all overdue issues (assuming 14 days is the standard loan period)
    overdue_issues = Issue.query.filter(
        Issue.return_date.is_(None),  # Not returned yet
        db.func.date(Issue.issue_date) < (today - timedelta(days=14))  # Overdue
    ).join(Student).join(Book).all()

    outstanding_fines = []
    for issue in overdue_issues:
        days_overdue = (today - issue.issue_date).days - 14  # 14 days grace period
        if days_overdue > 0:
            fine_amount = days_overdue * 2.0  # ₹2 per day fine
            outstanding_fines.append({
                'student_name': issue.student.name,
                'user_id': issue.student.user_id,
                'department': issue.student.department or 'N/A',
                'book_title': issue.book.title,
                'issue_date': issue.issue_date.strftime('%Y-%m-%d'),
                'days_overdue': days_overdue,
                'fine_amount': fine_amount,
                'issue_id': issue.issue_id
            })

    return jsonify({
        'fines': outstanding_fines,
        'total_amount': sum(f['fine_amount'] for f in outstanding_fines),
        'total_count': len(outstanding_fines)
    })

@app.route('/api/admin/payment-history')
def admin_payment_history():
    if session.get('user_role') != 'admin':
        return jsonify({'error': 'Unauthorized'}), 401

    try:
        # Get query parameters
        start_date = request.args.get('start_date')
        end_date = request.args.get('end_date')
        payment_type = request.args.get('payment_type')
        payment_method = request.args.get('payment_method')

        # For now, return simulated data since we don't have a payments table
        # In a real implementation, you would query the payments table
        from datetime import datetime, timedelta
        import random

        payments = []

        # Generate some sample payment records based on actual issues
        issues = Issue.query.filter(Issue.return_date.is_not(None)).limit(20).all()

        for i, issue in enumerate(issues):
            # Simulate some payments for returned books with fines
            if random.choice([True, False]):  # 50% chance of having a fine payment
                payment_date = issue.return_date or datetime.now().date()

                # Apply date filter if provided
                if start_date and payment_date < datetime.strptime(start_date, '%Y-%m-%d').date():
                    continue
                if end_date and payment_date > datetime.strptime(end_date, '%Y-%m-%d').date():
                    continue

                payment_types = ['fine', 'deposit', 'membership']
                payment_methods = ['cash', 'card', 'upi', 'online']

                p_type = random.choice(payment_types)
                p_method = random.choice(payment_methods)

                # Apply filters
                if payment_type and p_type != payment_type:
                    continue
                if payment_method and p_method != payment_method:
                    continue

                fine_amount = random.randint(10, 100)

                payments.append({
                    'receipt_no': f'RCP{str(i+1).zfill(3)}',
                    'payment_date': payment_date.strftime('%Y-%m-%d'),
                    'student_name': issue.student.name,
                    'user_id': issue.student.user_id,
                    'payment_type': p_type.title(),
                    'amount': fine_amount,
                    'method': p_method.upper(),
                    'processed_by': 'Admin',
                    'book_title': issue.book.title if p_type == 'fine' else 'N/A'
                })

        return jsonify({
            'payments': payments,
            'total_amount': sum(p['amount'] for p in payments),
            'total_count': len(payments)
        })

    except Exception as e:
        return jsonify({
            'payments': [],
            'total_amount': 0,
            'total_count': 0,
            'error': str(e)
        })

@app.route('/librarian/circulation/payments')
def librarian_payment_management():
    if session.get('user_role') != 'librarian':
        return redirect(url_for('login'))
    return render_template('librarian_payment_management.html')

@app.route('/admin/circulation/binding')
def admin_binding_management():
    if session.get('user_role') != 'admin':
        return redirect(url_for('login'))
    return render_template('admin_binding_management.html')

@app.route('/librarian/circulation/binding')
def librarian_binding_management():
    if session.get('user_role') != 'librarian':
        return redirect(url_for('login'))
    return render_template('librarian_binding_management.html')

@app.route('/admin/circulation/fines')
def admin_fine_management():
    if session.get('user_role') != 'admin':
        return redirect(url_for('login'))
    return render_template('admin_fine_management.html')

@app.route('/librarian/circulation/fines')
def librarian_fine_management():
    if session.get('user_role') != 'librarian':
        return redirect(url_for('login'))
    return render_template('librarian_fine_management.html')

# College and Department Management Routes
@app.route('/admin/colleges')
def admin_colleges():
    if session.get('user_role') != 'admin':
        return redirect(url_for('login'))
    colleges = get_colleges()
    return render_template('admin_colleges.html', colleges=colleges)

@app.route('/admin/departments')
def admin_departments():
    if session.get('user_role') != 'admin':
        return redirect(url_for('login'))
    colleges = get_colleges()
    departments = get_departments()
    return render_template('admin_departments.html', colleges=colleges, departments=departments)

@app.route('/api/colleges', methods=['GET', 'POST'])
def api_colleges():
    if session.get('user_role') != 'admin':
        return jsonify({'error': 'Unauthorized'}), 401

    if request.method == 'POST':
        data = request.get_json()
        try:
            college = College(
                name=data['name'],
                code=data['code'],
                address=data.get('address', ''),
                phone=data.get('phone', ''),
                email=data.get('email', '')
            )
            db.session.add(college)
            db.session.commit()
            return jsonify({'success': True, 'message': 'College added successfully'})
        except Exception as e:
            db.session.rollback()
            return jsonify({'error': str(e)}), 400

    # GET request
    colleges = get_colleges()
    return jsonify([{
        'id': c.id,
        'name': c.name,
        'code': c.code,
        'address': c.address,
        'phone': c.phone,
        'email': c.email,
        'is_active': c.is_active
    } for c in colleges])

@app.route('/api/departments', methods=['GET', 'POST'])
def api_departments():
    if session.get('user_role') != 'admin':
        return jsonify({'error': 'Unauthorized'}), 401

    if request.method == 'POST':
        data = request.get_json()
        try:
            department = Department(
                name=data['name'],
                code=data['code'],
                college_id=data['college_id'],
                head_of_department=data.get('head_of_department', ''),
                phone=data.get('phone', ''),
                email=data.get('email', '')
            )
            db.session.add(department)
            db.session.commit()
            return jsonify({'success': True, 'message': 'Department added successfully'})
        except Exception as e:
            db.session.rollback()
            return jsonify({'error': str(e)}), 400

    # GET request
    college_id = request.args.get('college_id')
    departments = get_departments(college_id)
    return jsonify([{
        'id': d.id,
        'name': d.name,
        'code': d.code,
        'college_id': d.college_id,
        'college_name': d.college.name,
        'head_of_department': d.head_of_department,
        'phone': d.phone,
        'email': d.email,
        'is_active': d.is_active
    } for d in departments])

@app.route('/api/departments/<int:college_id>')
def api_departments_by_college(college_id):
    """Get departments for a specific college - accessible to both admin and librarian"""
    if session.get('user_role') not in ['admin', 'librarian']:
        return jsonify({'error': 'Unauthorized'}), 401

    departments = Department.query.filter_by(college_id=college_id, is_active=True).all()
    return jsonify([{
        'id': d.id,
        'name': d.name,
        'code': d.code
    } for d in departments])

# AutoLib-Style Report Routes for Admin
@app.route('/admin/reports/library-connection')
def admin_library_connection_report():
    if session.get('user_role') != 'admin':
        return redirect(url_for('login'))
    return render_template('admin_library_connection_report.html')

@app.route('/api/admin/library-connection-data')
def admin_library_connection_data():
    if session.get('user_role') != 'admin':
        return jsonify({'error': 'Unauthorized'}), 401

    # Get filter parameters
    date_from = request.args.get('dateFrom')
    date_to = request.args.get('dateTo')
    user_type = request.args.get('userType')
    department = request.args.get('department')

    # For now, return real user data with simulated login sessions
    # In a real system, you'd have a UserSession table to track logins
    users_data = []

    # Get students
    students = Student.query.filter_by(is_active=True).all() if not user_type or user_type == 'student' else []
    for student in students:
        if department and student.department != department:
            continue
        users_data.append({
            'user': f"{student.name} ({student.user_id})",
            'type': 'Student',
            'department': student.department or 'N/A',
            'loginTime': '2024-01-15 09:30:00',  # Would come from session table
            'logoutTime': '2024-01-15 11:45:00',  # Would come from session table
            'duration': '2h 15m',
            'ipAddress': '*************',  # Would come from session table
            'status': 'Completed'
        })

    # Get librarians
    librarians = Librarian.query.all() if not user_type or user_type == 'librarian' else []
    for librarian in librarians:
        users_data.append({
            'user': f"{librarian.name} (LIB)",
            'type': 'Librarian',
            'department': 'Library',
            'loginTime': '2024-01-15 08:00:00',  # Would come from session table
            'logoutTime': 'Active',  # Would come from session table
            'duration': '6h 30m',
            'ipAddress': '************',  # Would come from session table
            'status': 'Active'
        })

    # Calculate statistics
    total_sessions = len(users_data)
    active_users = len([u for u in users_data if u['status'] == 'Active'])

    return jsonify({
        'data': users_data[:50],  # Limit to 50 records for performance
        'statistics': {
            'totalSessions': total_sessions,
            'avgDuration': '2h 30m',  # Would be calculated from actual data
            'peakHour': '10:00 AM',  # Would be calculated from actual data
            'activeUsers': active_users
        }
    })

@app.route('/admin/reports/access-register')
def admin_access_register_report():
    if session.get('user_role') != 'admin':
        return redirect(url_for('login'))
    return render_template('admin_access_register_report.html')

@app.route('/api/admin/access-register-data')
def admin_access_register_data():
    if session.get('user_role') != 'admin':
        return jsonify({'error': 'Unauthorized'}), 401

    # Get filter parameters
    date_from = request.args.get('dateFrom')
    date_to = request.args.get('dateTo')
    user_type = request.args.get('userType')
    department = request.args.get('department')

    # Get real issue data to simulate access register
    # In a real system, you'd have an AccessLog table
    access_data = []

    # Get recent issues as access records
    query = Issue.query.join(Student)
    if date_from:
        query = query.filter(db.func.date(Issue.issue_date) >= datetime.strptime(date_from, '%Y-%m-%d').date())
    if date_to:
        query = query.filter(db.func.date(Issue.issue_date) <= datetime.strptime(date_to, '%Y-%m-%d').date())
    issues = query.order_by(Issue.issue_date.desc()).limit(100).all()

    for i, issue in enumerate(issues, 1):
        student = issue.student
        if user_type and user_type != 'student':
            continue
        if department and student.department != department:
            continue

        access_data.append({
            'sno': i,
            'userId': student.user_id,
            'name': student.name,
            'department': student.department or 'N/A',
            'entryTime': issue.issue_date.strftime('%Y-%m-%d %H:%M:%S'),
            'exitTime': issue.return_date.strftime('%Y-%m-%d %H:%M:%S') if issue.return_date else 'Still Inside',
            'duration': '2h 30m',  # Would be calculated from entry/exit times
            'purpose': 'Book Issue/Return',
            'status': 'Completed' if issue.return_date else 'Inside'
        })

    # Calculate statistics
    total_entries = len(access_data)
    currently_inside = len([a for a in access_data if a['status'] == 'Inside'])

    return jsonify({
        'data': access_data,
        'statistics': {
            'totalEntries': total_entries,
            'avgDuration': '2h 15m',  # Would be calculated from actual data
            'peakTime': '10:30 AM',  # Would be calculated from actual data
            'currentlyInside': currently_inside
        }
    })

@app.route('/admin/reports/bibliography')
def admin_bibliography_report():
    if session.get('user_role') != 'admin':
        return redirect(url_for('login'))
    return render_template('admin_bibliography_report.html',
                         colleges=get_colleges_for_dropdown(),
                         departments=get_departments_for_dropdown())

@app.route('/admin/reports/counter')
def admin_counter_report():
    if session.get('user_role') != 'admin':
        return redirect(url_for('login'))
    return render_template('admin_counter_report.html')

@app.route('/admin/reports/statistics')
def admin_statistics_report():
    if session.get('user_role') != 'admin':
        return redirect(url_for('login'))
    return render_template('admin_statistics_report.html')

@app.route('/api/admin/statistics-data')
def admin_statistics_data():
    if session.get('user_role') != 'admin':
        return jsonify({'error': 'Unauthorized'}), 401

    try:
        # Get real statistics
        total_books = Book.query.count()
        total_ebooks = EBook.query.count() if 'EBook' in globals() else 0
        active_students = Student.query.filter_by(is_active=True).count()
        current_issues = Issue.query.filter(Issue.return_date.is_(None)).count()

        # Calculate utilization rate
        total_resources = total_books + total_ebooks
        utilization_rate = (current_issues / total_resources * 100) if total_resources > 0 else 0

        # Get department-wise statistics
        from sqlalchemy import func
        dept_stats = db.session.query(
            Student.department,
            func.count(Issue.issue_id).label('issue_count')
        ).join(Issue, Student.user_id == Issue.user_id, isouter=True)\
         .group_by(Student.department)\
         .all()

        department_data = []
        for dept, count in dept_stats:
            if dept:  # Only include departments with names
                department_data.append({
                    'department': dept,
                    'issues': count or 0
                })

        # Get monthly circulation trends (last 6 months)
        from datetime import date, timedelta
        import calendar

        circulation_trends = []
        for i in range(6):
            month_start = date.today().replace(day=1) - timedelta(days=i*30)
            month_name = calendar.month_name[month_start.month]

            # Count issues for this month (simplified)
            month_issues = Issue.query.filter(
                Issue.issue_date >= month_start,
                Issue.issue_date < month_start + timedelta(days=30)
            ).count()

            circulation_trends.append({
                'month': month_name[:3],  # Short month name
                'issues': month_issues
            })

        circulation_trends.reverse()  # Show oldest to newest

        return jsonify({
            'summary': {
                'total_books': total_books + total_ebooks,
                'active_members': active_students,
                'current_issues': current_issues,
                'utilization_rate': round(utilization_rate, 1)
            },
            'department_stats': department_data,
            'circulation_trends': circulation_trends
        })
    except Exception as e:
        return jsonify({
            'summary': {
                'total_books': 0,
                'active_members': 0,
                'current_issues': 0,
                'utilization_rate': 0
            },
            'department_stats': [],
            'circulation_trends': [],
            'error': str(e)
        })

@app.route('/admin/reports/binding')
def admin_binding_report():
    if session.get('user_role') != 'admin':
        return redirect(url_for('login'))
    return render_template('admin_binding_report.html')

@app.route('/admin/reports/database')
def admin_database_report():
    if session.get('user_role') != 'admin':
        return redirect(url_for('login'))
    return render_template('admin_database_report.html')

@app.route('/api/admin/database-statistics')
def admin_database_statistics():
    if session.get('user_role') != 'admin':
        return jsonify({'error': 'Unauthorized'}), 401

    try:
        # Get real database statistics
        tables_info = []

        # Books table
        books_count = Book.query.count()
        tables_info.append({
            'name': 'Books',
            'icon': 'fas fa-book',
            'count': books_count,
            'size': f"{books_count * 2}",  # Estimated size in KB
            'status': 'Healthy' if books_count >= 0 else 'Warning',
            'last_modified': 'Recent'
        })

        # Students table
        students_count = Student.query.count()
        tables_info.append({
            'name': 'Students',
            'icon': 'fas fa-graduation-cap',
            'count': students_count,
            'size': f"{students_count * 1}",  # Estimated size in KB
            'status': 'Healthy' if students_count >= 0 else 'Warning',
            'last_modified': 'Recent'
        })

        # Issues table
        issues_count = Issue.query.count()
        tables_info.append({
            'name': 'Issues',
            'icon': 'fas fa-exchange-alt',
            'count': issues_count,
            'size': f"{issues_count * 1}",  # Estimated size in KB
            'status': 'Healthy' if issues_count >= 0 else 'Warning',
            'last_modified': 'Recent'
        })

        # Librarians table
        librarians_count = Librarian.query.count()
        tables_info.append({
            'name': 'Librarians',
            'icon': 'fas fa-user-tie',
            'count': librarians_count,
            'size': f"{librarians_count * 1}",  # Estimated size in KB
            'status': 'Healthy' if librarians_count >= 0 else 'Warning',
            'last_modified': 'Recent'
        })

        # Colleges table
        colleges_count = College.query.count()
        tables_info.append({
            'name': 'Colleges',
            'icon': 'fas fa-university',
            'count': colleges_count,
            'size': f"{colleges_count * 1}",  # Estimated size in KB
            'status': 'Healthy' if colleges_count >= 0 else 'Warning',
            'last_modified': 'Recent'
        })

        # Departments table
        departments_count = Department.query.count()
        tables_info.append({
            'name': 'Departments',
            'icon': 'fas fa-building',
            'count': departments_count,
            'size': f"{departments_count * 1}",  # Estimated size in KB
            'status': 'Healthy' if departments_count >= 0 else 'Warning',
            'last_modified': 'Recent'
        })

        # Calculate totals
        total_records = sum(table['count'] for table in tables_info)
        total_size = sum(int(table['size']) for table in tables_info)

        return jsonify({
            'tables': tables_info,
            'summary': {
                'total_records': total_records,
                'total_size': total_size,
                'total_tables': len(tables_info),
                'database_status': 'Healthy'
            }
        })
    except Exception as e:
        return jsonify({
            'tables': [],
            'summary': {
                'total_records': 0,
                'total_size': 0,
                'total_tables': 0,
                'database_status': 'Error'
            },
            'error': str(e)
        })

@app.route('/admin/reports/member')
def admin_member_report():
    if session.get('user_role') != 'admin':
        return redirect(url_for('login'))
    return render_template('admin_member_report.html')

@app.route('/admin/reports/resource')
def admin_resource_report():
    if session.get('user_role') != 'admin':
        return redirect(url_for('login'))
    return render_template('admin_resource_report.html')

@app.route('/admin/reports/no-dues')
def admin_no_dues_report():
    if session.get('user_role') != 'admin':
        return redirect(url_for('login'))
    return render_template('admin_no_dues_report.html')

@app.route('/admin/reports/qb')
def admin_qb_report():
    if session.get('user_role') != 'admin':
        return redirect(url_for('login'))
    return render_template('admin_qb_report.html')

@app.route('/admin/reports/transfer')
def admin_transfer_report():
    if session.get('user_role') != 'admin':
        return redirect(url_for('login'))
    return render_template('admin_transfer_report.html')

@app.route('/admin/reports/missing')
def admin_missing_report():
    if session.get('user_role') != 'admin':
        return redirect(url_for('login'))
    return render_template('admin_missing_report.html')

@app.route('/admin/reports/news-clipping')
def admin_news_clipping_report():
    if session.get('user_role') != 'admin':
        return redirect(url_for('login'))
    return render_template('admin_news_clipping_report.html',
                         colleges=get_colleges_for_dropdown(),
                         departments=get_departments_for_dropdown())

# Gate Management Routes
@app.route('/admin/gate-management')
def admin_gate_management():
    if session.get('user_role') != 'admin':
        return redirect(url_for('login'))

    gate_users = GateUser.query.filter_by(is_active=True).all()
    return render_template('admin_gate_management.html', gate_users=gate_users)

@app.route('/admin/gate-management/add-user', methods=['POST'])
def admin_add_gate_user():
    if session.get('user_role') != 'admin':
        return jsonify({'error': 'Unauthorized'}), 401

    try:
        username = request.form.get('username')
        password = request.form.get('password')

        # Validate required fields
        if not all([username, password]):
            return jsonify({'error': 'Username and password are required'}), 400

        # Check if username already exists
        existing_user = GateUser.query.filter_by(username=username).first()
        if existing_user:
            return jsonify({'error': 'Username already exists'}), 400

        # Hash password
        from werkzeug.security import generate_password_hash
        hashed_password = generate_password_hash(password)

        # Create new gate user
        gate_user = GateUser(
            username=username,
            password=hashed_password,
            role='gate_operator',
            created_by=session.get('user_id', 'admin')
        )

        db.session.add(gate_user)
        db.session.commit()

        return jsonify({'success': True, 'message': 'Gate user created successfully'})

    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@app.route('/admin/gate-management/delete-user/<int:user_id>', methods=['POST'])
def admin_delete_gate_user(user_id):
    if session.get('user_role') != 'admin':
        return jsonify({'error': 'Unauthorized'}), 401

    try:
        gate_user = GateUser.query.get_or_404(user_id)
        gate_user.is_active = False
        db.session.commit()

        return jsonify({'success': True, 'message': 'Gate user deactivated successfully'})

    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

# Gate Entry Dashboard Routes
@app.route('/gate-login', methods=['GET', 'POST'])
def gate_login():
    if request.method == 'POST':
        username = request.form.get('username')
        password = request.form.get('password')

        if not username or not password:
            flash('Username and password are required', 'error')
            return render_template('gate_login.html')

        # Check gate user credentials
        from werkzeug.security import check_password_hash
        gate_user = GateUser.query.filter_by(username=username, is_active=True).first()

        if gate_user and check_password_hash(gate_user.password, password):
            # Update last login
            gate_user.last_login = datetime.utcnow()
            db.session.commit()

            # Set session
            session['gate_user_id'] = gate_user.gate_user_id
            session['gate_username'] = gate_user.username
            session['gate_role'] = gate_user.role

            return redirect(url_for('gate_dashboard'))
        else:
            flash('Invalid username or password', 'error')

    return render_template('gate_login.html')

@app.route('/gate-dashboard')
def gate_dashboard():
    if 'gate_user_id' not in session:
        return redirect(url_for('gate_login'))

    # Get today's statistics
    from datetime import date
    today = date.today()

    # Get total entries today
    total_entries_today = GateEntry.query.filter(
        db.func.date(GateEntry.entry_time) == today
    ).count()

    # Get currently inside count (entries without exit time)
    currently_inside = GateEntry.query.filter(
        GateEntry.entry_type == 'IN',
        GateEntry.exit_time.is_(None)
    ).count()

    return render_template('gate_dashboard.html',
                         total_entries_today=total_entries_today,
                         currently_inside=currently_inside)

@app.route('/gate-scan', methods=['POST'])
def gate_scan():
    if 'gate_user_id' not in session:
        return jsonify({'error': 'Unauthorized'}), 401

    try:
        user_id = request.json.get('user_id')
        if not user_id:
            return jsonify({'error': 'User ID is required'}), 400

        # Find student
        student = Student.query.filter_by(user_id=user_id).first()

        if not student:
            return jsonify({
                'success': False,
                'message': 'Student not found in database',
                'user_id': user_id,
                'is_valid': False
            })

        # Check if student has an active entry (is inside)
        active_entry = GateEntry.query.filter_by(
            user_id=user_id,
            exit_time=None
        ).order_by(GateEntry.entry_time.desc()).first()

        from datetime import datetime, timezone
        current_time = datetime.now(timezone.utc)

        if active_entry:
            # Student is exiting
            active_entry.exit_time = current_time
            entry_type = 'OUT'
            message = f'Exit recorded for {student.name}'
        else:
            # Student is entering
            entry_type = 'IN'
            message = f'Entry recorded for {student.name}'

            # Create new gate entry record for entry
            gate_entry = GateEntry(
                user_id=user_id,
                student_id=student.id,
                entry_time=current_time,
                exit_time=None,
                entry_type=entry_type,
                gate_operator=session.get('gate_username'),
                is_valid_entry=True
            )
            db.session.add(gate_entry)

        db.session.commit()

        return jsonify({
            'success': True,
            'message': message,
            'user_id': user_id,
            'student_name': student.name,
            'college': student.college.name if student.college else 'N/A',
            'department': student.department.name if student.department else 'N/A',
            'entry_type': entry_type,
            'time': current_time.strftime('%Y-%m-%d %H:%M:%S'),
            'is_valid': True
        })

    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@app.route('/gate-logout')
def gate_logout():
    session.pop('gate_user_id', None)
    session.pop('gate_username', None)
    session.pop('gate_role', None)
    return redirect(url_for('gate_login'))

# Gate Reports
@app.route('/admin/reports/gate')
def admin_gate_report():
    if session.get('user_role') != 'admin':
        return redirect(url_for('login'))
    return render_template('admin_gate_report.html',
                         colleges=get_colleges_for_dropdown(),
                         departments=get_departments_for_dropdown())

@app.route('/api/admin/gate-entries')
def admin_gate_entries_api():
    if session.get('user_role') != 'admin':
        return jsonify({'error': 'Unauthorized'}), 401

    try:
        # Get query parameters for filtering
        college_id = request.args.get('college_id')
        department_id = request.args.get('department_id')
        start_date = request.args.get('start_date')
        end_date = request.args.get('end_date')
        entry_type = request.args.get('entry_type')
        user_id = request.args.get('user_id')

        # Build query
        query = db.session.query(GateEntry).join(Student, GateEntry.student_id == Student.id, isouter=True)

        if college_id:
            query = query.filter(Student.college_id == college_id)
        if department_id:
            query = query.filter(Student.department_id == department_id)
        if entry_type:
            query = query.filter(GateEntry.entry_type == entry_type)
        if user_id:
            query = query.filter(GateEntry.user_id.like(f'%{user_id}%'))
        if start_date:
            from datetime import datetime
            start_date_obj = datetime.strptime(start_date, '%Y-%m-%d')
            query = query.filter(GateEntry.entry_time >= start_date_obj)
        if end_date:
            from datetime import datetime
            end_date_obj = datetime.strptime(end_date + ' 23:59:59', '%Y-%m-%d %H:%M:%S')
            query = query.filter(GateEntry.entry_time <= end_date_obj)

        # Get results
        entries = query.order_by(GateEntry.entry_time.desc()).all()

        entries_data = []
        for entry in entries:
            entries_data.append({
                'entry_id': entry.entry_id,
                'user_id': entry.user_id,
                'student_name': entry.student.name if entry.student else 'Unknown',
                'college_name': entry.student.college.name if entry.student and entry.student.college else 'N/A',
                'department_name': entry.student.department.name if entry.student and entry.student.department else 'N/A',
                'entry_time': entry.entry_time.strftime('%Y-%m-%d %H:%M:%S'),
                'exit_time': entry.exit_time.strftime('%Y-%m-%d %H:%M:%S') if entry.exit_time else 'Still Inside',
                'entry_type': entry.entry_type,
                'gate_operator': entry.gate_operator,
                'is_valid_entry': entry.is_valid_entry,
                'duration': calculate_duration(entry.entry_time, entry.exit_time) if entry.exit_time else 'Ongoing'
            })

        return jsonify({
            'entries': entries_data,
            'total_count': len(entries_data)
        })

    except Exception as e:
        return jsonify({
            'entries': [],
            'total_count': 0,
            'error': str(e)
        })

def calculate_duration(entry_time, exit_time):
    """Calculate duration between entry and exit time"""
    if not exit_time:
        return 'Ongoing'

    duration = exit_time - entry_time
    hours = duration.seconds // 3600
    minutes = (duration.seconds % 3600) // 60

    if duration.days > 0:
        return f"{duration.days}d {hours}h {minutes}m"
    else:
        return f"{hours}h {minutes}m"

@app.route('/api/check-scanner')
def check_scanner():
    """Check for connected barcode scanner devices"""
    try:
        import platform
        import subprocess

        scanner_detected = False
        scanner_info = []

        system = platform.system()

        if system == "Windows":
            # Check Windows devices using wmic
            try:
                result = subprocess.run([
                    'wmic', 'path', 'Win32_PnPEntity', 'where',
                    'DeviceID like "%HID%"', 'get', 'Name,DeviceID'
                ], capture_output=True, text=True, timeout=10)

                if result.returncode == 0:
                    output = result.stdout.lower()
                    scanner_keywords = ['scanner', 'barcode', 'symbol', 'honeywell', 'zebra', 'datalogic']

                    for keyword in scanner_keywords:
                        if keyword in output:
                            scanner_detected = True
                            scanner_info.append(f"Detected device containing '{keyword}'")
                            break

            except Exception as e:
                print(f"Windows device check failed: {e}")

        elif system == "Linux":
            # Check Linux devices using lsusb
            try:
                result = subprocess.run(['lsusb'], capture_output=True, text=True, timeout=10)

                if result.returncode == 0:
                    output = result.stdout.lower()
                    scanner_keywords = ['scanner', 'barcode', 'symbol', 'honeywell', 'zebra']

                    for keyword in scanner_keywords:
                        if keyword in output:
                            scanner_detected = True
                            scanner_info.append(f"USB device containing '{keyword}' found")
                            break

            except Exception as e:
                print(f"Linux device check failed: {e}")

        elif system == "Darwin":  # macOS
            # Check macOS devices using system_profiler
            try:
                result = subprocess.run([
                    'system_profiler', 'SPUSBDataType'
                ], capture_output=True, text=True, timeout=10)

                if result.returncode == 0:
                    output = result.stdout.lower()
                    scanner_keywords = ['scanner', 'barcode', 'symbol', 'honeywell', 'zebra']

                    for keyword in scanner_keywords:
                        if keyword in output:
                            scanner_detected = True
                            scanner_info.append(f"USB device containing '{keyword}' found")
                            break

            except Exception as e:
                print(f"macOS device check failed: {e}")

        return jsonify({
            'scanner_detected': scanner_detected,
            'system': system,
            'info': scanner_info,
            'message': 'Scanner detected' if scanner_detected else 'No barcode scanner detected'
        })

    except Exception as e:
        return jsonify({
            'scanner_detected': False,
            'error': str(e),
            'message': 'Error checking for scanner devices'
        })

# Librarian Report Routes
@app.route('/librarian/reports/access-register')
def librarian_access_register_report():
    if session.get('user_role') != 'librarian':
        return redirect(url_for('login'))
    return render_template('librarian_access_register_report.html')

@app.route('/librarian/reports/bibliography')
def librarian_bibliography_report():
    if session.get('user_role') != 'librarian':
        return redirect(url_for('login'))
    return render_template('librarian_bibliography_report.html')

@app.route('/librarian/reports/counter')
def librarian_counter_report():
    if session.get('user_role') != 'librarian':
        return redirect(url_for('login'))
    return render_template('librarian_counter_report.html')

@app.route('/librarian/reports/statistics')
def librarian_statistics_report():
    if session.get('user_role') != 'librarian':
        return redirect(url_for('login'))
    return render_template('librarian_statistics_report.html')

@app.route('/librarian/reports/member')
def librarian_member_report():
    if session.get('user_role') != 'librarian':
        return redirect(url_for('login'))
    return render_template('librarian_member_report.html')

@app.route('/librarian/reports/resource')
def librarian_resource_report():
    if session.get('user_role') != 'librarian':
        return redirect(url_for('login'))
    return render_template('librarian_resource_report.html')

@app.route('/librarian/reports/no-dues')
def librarian_no_dues_report():
    if session.get('user_role') != 'librarian':
        return redirect(url_for('login'))
    return render_template('librarian_no_dues_report.html')

@app.route('/librarian/reports/qb')
def librarian_qb_report():
    if session.get('user_role') != 'librarian':
        return redirect(url_for('login'))
    return render_template('librarian_qb_report.html')

@app.route('/librarian/reports/transfer')
def librarian_transfer_report():
    if session.get('user_role') != 'librarian':
        return redirect(url_for('login'))
    return render_template('librarian_transfer_report.html')

@app.route('/librarian/reports/missing')
def librarian_missing_report():
    if session.get('user_role') != 'librarian':
        return redirect(url_for('login'))
    return render_template('librarian_missing_report.html')

@app.route('/librarian/reports/news-clipping')
def librarian_news_clipping_report():
    if session.get('user_role') != 'librarian':
        return redirect(url_for('login'))
    return render_template('librarian_news_clipping_report.html',
                         colleges=get_colleges_for_dropdown(),
                         departments=get_departments_for_dropdown())

# Librarian News Clipping Management
@app.route('/librarian/news-clippings')
def librarian_news_clippings():
    if session.get('user_role') != 'librarian':
        return redirect(url_for('login'))
    return render_template('librarian_news_clippings.html',
                         colleges=get_colleges_for_dropdown(),
                         departments=get_departments_for_dropdown())

@app.route('/librarian/news-clippings/add', methods=['GET', 'POST'])
def librarian_add_news_clipping():
    if session.get('user_role') != 'librarian':
        return redirect(url_for('login'))

    if request.method == 'POST':
        try:
            # Get form data
            clipping_no = request.form.get('clipping_no')
            newspaper_name = request.form.get('newspaper_name')
            news_type = request.form.get('news_type')
            date_str = request.form.get('date')
            pages = request.form.get('pages')
            keywords = request.form.get('keywords')
            abstract = request.form.get('abstract')
            content = request.form.get('content')
            college_id = request.form.get('college_id')
            department_id = request.form.get('department_id')

            # Validate required fields
            if not all([clipping_no, newspaper_name, news_type, date_str, pages, keywords, abstract, content, college_id, department_id]):
                flash('All fields are required!', 'error')
                return redirect(url_for('librarian_add_news_clipping'))

            # Check if clipping number already exists
            existing_clipping = NewsClipping.query.filter_by(clipping_no=clipping_no).first()
            if existing_clipping:
                flash('News clipping number already exists!', 'error')
                return redirect(url_for('librarian_add_news_clipping'))

            # Parse date
            from datetime import datetime
            date_obj = datetime.strptime(date_str, '%Y-%m-%d').date()

            # Create new news clipping
            news_clipping = NewsClipping(
                clipping_no=clipping_no,
                newspaper_name=newspaper_name,
                news_type=news_type,
                date=date_obj,
                pages=pages,
                keywords=keywords,
                abstract=abstract,
                content=content,
                college_id=int(college_id),
                department_id=int(department_id),
                created_by=session.get('user_id', 'librarian')
            )

            db.session.add(news_clipping)
            db.session.commit()

            flash('News clipping added successfully!', 'success')
            return redirect(url_for('librarian_news_clippings'))

        except Exception as e:
            db.session.rollback()
            flash(f'Error adding news clipping: {str(e)}', 'error')
            return redirect(url_for('librarian_add_news_clipping'))

    return render_template('librarian_add_news_clipping.html',
                         colleges=get_colleges_for_dropdown(),
                         departments=get_departments_for_dropdown())

# Additional Librarian Report Routes
@app.route('/librarian/reports/library-connection')
def librarian_library_connection_report():
    if session.get('user_role') != 'librarian':
        return redirect(url_for('login'))
    return render_template('librarian_library_connection_report.html')

@app.route('/librarian/reports/binding')
def librarian_binding_report():
    if session.get('user_role') != 'librarian':
        return redirect(url_for('login'))
    return render_template('librarian_binding_report.html')

@app.route('/librarian/reports/database')
def librarian_database_report():
    if session.get('user_role') != 'librarian':
        return redirect(url_for('login'))
    return render_template('librarian_database_report.html')

@app.route('/admin/reports')
def admin_reports():
    if session.get('user_role') != 'admin':
        return redirect(url_for('login', role='admin'))
    return render_template('admin_reports.html')

# Export Routes for Admin Reports
@app.route('/admin/reports/export/<report_type>/<format>')
def admin_export_report(report_type, format):
    if session.get('user_role') != 'admin':
        return redirect(url_for('login', role='admin'))

    try:
        # Get data based on report type
        data = []
        title = ""

        if report_type == 'books':
            books = Book.query.all()
            data = [{
                'Access No': book.access_no,
                'Title': book.title,
                'Author': book.author,
                'Publisher': book.publisher,
                'Subject': book.subject,
                'Department': book.department,
                'Category': book.category,
                'Location': book.location,
                'Copies': book.copies,
                'Available': book.available_count
            } for book in books]
            title = "Books Report"

        elif report_type == 'students':
            students = Student.query.all()
            data = [{
                'User ID': student.user_id,
                'Name': student.name,
                'Email': student.email,
                'Department': student.department,
                'Course': student.course,
                'Year': student.current_year,
                'Validity Date': student.validity_date.strftime('%Y-%m-%d') if student.validity_date else 'N/A'
            } for student in students]
            title = "Students Report"

        elif report_type == 'issues':
            issues = Issue.query.join(Book).join(Student).all()
            data = [{
                'Issue ID': issue.issue_id,
                'Book Title': issue.book.title,
                'Student Name': issue.student.name,
                'Issue Date': issue.issue_date.strftime('%Y-%m-%d'),
                'Due Date': issue.due_date.strftime('%Y-%m-%d'),
                'Return Date': issue.return_date.strftime('%Y-%m-%d') if issue.return_date else 'Not Returned',
                'Fine': f"₹{issue.fine:.2f}"
            } for issue in issues]
            title = "Issues Report"

        elif report_type == 'overdue':
            overdue_issues = Issue.query.filter(
                Issue.return_date == None,
                Issue.due_date < datetime.now().date()
            ).join(Book).join(Student).all()
            data = [{
                'Issue ID': issue.issue_id,
                'Book Title': issue.book.title,
                'Student Name': issue.student.name,
                'Student Email': issue.student.email,
                'Issue Date': issue.issue_date.strftime('%Y-%m-%d'),
                'Due Date': issue.due_date.strftime('%Y-%m-%d'),
                'Days Overdue': (datetime.now().date() - issue.due_date).days,
                'Fine': f"₹{calculate_fine(issue.due_date):.2f}"
            } for issue in overdue_issues]
            title = "Overdue Books Report"

        # Export based on format
        if format == 'excel':
            output = export_to_excel(data, f"{report_type}_report.xlsx", title)
            if output:
                return send_file(
                    output,
                    mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                    as_attachment=True,
                    download_name=f"{report_type}_report.xlsx"
                )
        elif format == 'pdf':
            output = export_to_pdf(data, f"{report_type}_report.pdf", title)
            if output:
                return send_file(
                    output,
                    mimetype='application/pdf',
                    as_attachment=True,
                    download_name=f"{report_type}_report.pdf"
                )

        flash('Error generating report. Please try again.')
        return redirect(url_for('admin_reports'))

    except Exception as e:
        flash(f'Error exporting report: {str(e)}')
        return redirect(url_for('admin_reports'))

# Export Routes for Librarian Reports
@app.route('/librarian/reports/export/<report_type>/<format>')
def librarian_export_report(report_type, format):
    if session.get('user_role') != 'librarian':
        return redirect(url_for('login', role='librarian'))

    try:
        # Get data based on report type (same logic as admin but for librarian)
        data = []
        title = ""

        if report_type == 'books':
            books = Book.query.all()
            data = [{
                'Access No': book.access_no,
                'Title': book.title,
                'Author': book.author,
                'Publisher': book.publisher,
                'Subject': book.subject,
                'Department': book.department,
                'Category': book.category,
                'Location': book.location,
                'Copies': book.copies,
                'Available': book.available_count
            } for book in books]
            title = "Books Report"

        elif report_type == 'students':
            students = Student.query.all()
            data = [{
                'User ID': student.user_id,
                'Name': student.name,
                'Email': student.email,
                'Department': student.department,
                'Course': student.course,
                'Year': student.current_year,
                'Validity Date': student.validity_date.strftime('%Y-%m-%d') if student.validity_date else 'N/A'
            } for student in students]
            title = "Students Report"

        elif report_type == 'issues':
            issues = Issue.query.join(Book).join(Student).all()
            data = [{
                'Issue ID': issue.issue_id,
                'Book Title': issue.book.title,
                'Student Name': issue.student.name,
                'Issue Date': issue.issue_date.strftime('%Y-%m-%d'),
                'Due Date': issue.due_date.strftime('%Y-%m-%d'),
                'Return Date': issue.return_date.strftime('%Y-%m-%d') if issue.return_date else 'Not Returned',
                'Fine': f"₹{issue.fine:.2f}"
            } for issue in issues]
            title = "Issues Report"

        # Export based on format
        if format == 'excel':
            output = export_to_excel(data, f"{report_type}_report.xlsx", title)
            if output:
                return send_file(
                    output,
                    mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                    as_attachment=True,
                    download_name=f"{report_type}_report.xlsx"
                )
        elif format == 'pdf':
            output = export_to_pdf(data, f"{report_type}_report.pdf", title)
            if output:
                return send_file(
                    output,
                    mimetype='application/pdf',
                    as_attachment=True,
                    download_name=f"{report_type}_report.pdf"
                )

        flash('Error generating report. Please try again.')
        return redirect(url_for('librarian_dashboard'))

    except Exception as e:
        flash(f'Error exporting report: {str(e)}')
        return redirect(url_for('librarian_dashboard'))

@app.route('/api/admin/quick-statistics')
def admin_quick_statistics():
    if session.get('user_role') != 'admin':
        return jsonify({'error': 'Unauthorized'}), 401

    try:
        # Get real statistics from database
        total_books = Book.query.count()
        total_ebooks = EBook.query.count() if 'EBook' in globals() else 0
        active_students = Student.query.filter_by(is_active=True).count()
        active_librarians = Librarian.query.count()
        current_issues = Issue.query.filter(Issue.return_date.is_(None)).count()

        # Calculate overdue books (assuming 14 days loan period)
        from datetime import date, timedelta
        overdue_date = date.today() - timedelta(days=14)
        overdue_books = Issue.query.filter(
            Issue.return_date == None,
            Issue.due_date < overdue_date
        ).count()

        return jsonify({
            'total_books': total_books + total_ebooks,
            'active_members': active_students + active_librarians,
            'current_issues': current_issues,
            'overdue_books': overdue_books
        })
    except Exception as e:
        return jsonify({
            'total_books': 0,
            'active_members': 0,
            'current_issues': 0,
            'overdue_books': 0,
            'error': str(e)
        })

@app.route('/admin/ebooks')
def admin_ebooks():
    if session.get('user_role') != 'admin':
        return redirect(url_for('login', role='admin'))

    ebooks = EBook.query.all()
    return render_template('admin_ebooks.html', ebooks=ebooks)

@app.route('/admin/add_ebook', methods=['GET', 'POST'])
def admin_add_ebook():
    if session.get('user_role') != 'admin':
        return redirect(url_for('login', role='admin'))

    if request.method == 'POST':
        try:
            # Check if access number already exists
            if EBook.query.filter_by(access_no=request.form['access_no']).first():
                flash('Access number already exists!')
                return render_template('admin_add_ebook.html', departments=get_departments_for_dropdown(), colleges=get_colleges_for_dropdown())

            ebook = EBook(
                access_no=request.form['access_no'],
                title=request.form['title'],
                author=request.form['author'],
                publisher=request.form['publisher'],
                subject=request.form['subject'],
                department=request.form['department'],
                category=request.form['category'],
                file_format=request.form['file_format'],
                file_size=request.form.get('file_size', ''),
                download_url=request.form.get('download_url', ''),
                isbn=request.form.get('isbn', ''),
                pages=int(request.form['pages']) if request.form.get('pages') else None,
                language=request.form.get('language', 'English'),
                description=request.form.get('description', '')
            )
            db.session.add(ebook)
            db.session.commit()
            flash('E-book added successfully!')
            return redirect(url_for('admin_ebooks'))

        except ValueError as e:
            flash('Invalid input. Please check your data.')
            return render_template('admin_add_ebook.html', departments=get_departments_for_dropdown(), colleges=get_colleges_for_dropdown())
        except Exception as e:
            flash(f'Error adding e-book: {str(e)}')
            return render_template('admin_add_ebook.html', departments=get_departments_for_dropdown(), colleges=get_colleges_for_dropdown())

    return render_template('admin_add_ebook.html', departments=get_departments_for_dropdown(), colleges=get_colleges_for_dropdown())

@app.route('/admin/bulk_books', methods=['GET', 'POST'])
def admin_bulk_books():
    if session.get('user_role') != 'admin':
        return redirect(url_for('login', role='admin'))

    if request.method == 'POST':
        if 'file' not in request.files:
            flash('No file selected!')
            return redirect(request.url)

        file = request.files['file']
        if file.filename == '':
            flash('No file selected!')
            return redirect(request.url)

        if file and allowed_file(file.filename):
            try:
                # Read Excel file
                df = pd.read_excel(file)

                # Validate required columns
                required_columns = ['title', 'author', 'publisher', 'isbn', 'subject', 'department']
                missing_columns = [col for col in required_columns if col not in df.columns]

                if missing_columns:
                    flash(f'Missing required columns: {", ".join(missing_columns)}')
                    return redirect(request.url)

                success_count = 0
                error_count = 0
                errors = []

                for index, row in df.iterrows():
                    try:
                        # Auto-generate access number if not provided
                        access_no = str(row.get('access_no', '')).strip()
                        if not access_no:
                            access_no = generate_next_access_number('BK')
                        else:
                            # Check if provided access number already exists
                            if Book.query.filter_by(access_no=access_no).first():
                                access_no = generate_next_access_number('BK')
                                errors.append(f'Row {index + 2}: Access number {row["access_no"]} already exists, auto-generated {access_no}')

                        # Check if book already exists by ISBN
                        existing_book = Book.query.filter_by(isbn=row['isbn']).first()
                        if existing_book:
                            errors.append(f'Row {index + 2}: Book with ISBN {row["isbn"]} already exists')
                            error_count += 1
                            continue

                        book = Book(
                            access_no=access_no,
                            title=str(row['title']),
                            author=str(row['author']),
                            publisher=str(row['publisher']),
                            isbn=str(row['isbn']),
                            subject=str(row['subject']),
                            department=str(row['department']),
                            category=str(row.get('category', 'textbook')),
                            total_count=int(row.get('total_count', 1)),
                            available_count=int(row.get('available_count', row.get('total_count', 1))),
                            publication_year=int(row.get('publication_year', 2024)),
                            pages=int(row.get('pages', 0)),
                            language=str(row.get('language', 'English')),
                            location=str(row.get('location', 'A1')),
                            price=float(row.get('price', 0.0)),
                            description=str(row.get('description', ''))
                        )

                        db.session.add(book)
                        success_count += 1

                    except Exception as e:
                        errors.append(f'Row {index + 2}: {str(e)}')
                        error_count += 1

                if success_count > 0:
                    db.session.commit()
                    flash(f'Successfully added {success_count} books!')

                if error_count > 0:
                    flash(f'{error_count} books failed to add. Check errors below.', 'warning')
                    for error in errors[:10]:  # Show first 10 errors
                        flash(error, 'danger')

                return redirect(url_for('admin_books'))

            except Exception as e:
                flash(f'Error processing file: {str(e)}')
                return redirect(request.url)
        else:
            flash('Invalid file format. Please upload an Excel file (.xlsx or .xls)')
            return redirect(request.url)

    return render_template('admin_bulk_books.html', departments=get_departments_for_dropdown(), colleges=get_colleges_for_dropdown())

@app.route('/admin/download_book_template')
def admin_download_book_template():
    if session.get('user_role') != 'admin':
        return redirect(url_for('login', role='admin'))

    # Create a sample Excel template for book upload
    import io
    from openpyxl import Workbook
    from openpyxl.styles import Font, PatternFill

    wb = Workbook()
    ws = wb.active
    ws.title = "Book Upload Template"

    # Headers
    headers = [
        'title', 'author', 'publisher', 'isbn', 'subject', 'department',
        'category', 'total_count', 'available_count', 'publication_year',
        'pages', 'language', 'location', 'price', 'description'
    ]

    # Add headers with formatting
    for col, header in enumerate(headers, 1):
        cell = ws.cell(row=1, column=col, value=header)
        cell.font = Font(bold=True)
        cell.fill = PatternFill(start_color="CCCCCC", end_color="CCCCCC", fill_type="solid")

    # Add sample data
    sample_data = [
        'Introduction to Computer Science', 'John Smith', 'Tech Publishers',
        '978-0123456789', 'Computer Science', 'CSE', 'textbook', 5, 5, 2024,
        450, 'English', 'A1-S1', 1200.00, 'Comprehensive introduction to CS concepts'
    ]

    for col, value in enumerate(sample_data, 1):
        ws.cell(row=2, column=col, value=value)

    # Auto-adjust column widths
    for column in ws.columns:
        max_length = 0
        column_letter = column[0].column_letter
        for cell in column:
            try:
                if len(str(cell.value)) > max_length:
                    max_length = len(str(cell.value))
            except:
                pass
        adjusted_width = min(max_length + 2, 50)
        ws.column_dimensions[column_letter].width = adjusted_width

    # Save to BytesIO
    output = io.BytesIO()
    wb.save(output)
    output.seek(0)

    return send_file(
        output,
        mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        as_attachment=True,
        download_name='book_upload_template.xlsx'
    )

@app.route('/admin/bulk_ebooks', methods=['GET', 'POST'])
def admin_bulk_ebooks():
    if session.get('user_role') != 'admin':
        return redirect(url_for('login', role='admin'))

    if request.method == 'POST':
        if 'file' not in request.files:
            flash('No file selected!')
            return redirect(request.url)
        
        file = request.files['file']
        if file.filename == '':
            flash('No file selected!')
            return redirect(request.url)
        
        if file and allowed_file(file.filename):
            try:
                # Read Excel file with comprehensive error handling
                try:
                    df = pd.read_excel(file, engine='openpyxl')
                except Exception as excel_error:
                    error_msg = str(excel_error).lower()

                    # Try with xlrd engine for older .xls files
                    if file.filename.endswith('.xls'):
                        try:
                            df = pd.read_excel(file, engine='xlrd')
                        except Exception as xlrd_error:
                            flash('Error: Unable to read the .xls file. Please save your file as .xlsx format and try again.')
                            return render_template('admin_bulk_ebooks.html')
                    else:
                        # Handle specific error types for .xlsx files
                        if "not a zip file" in error_msg or "bad zipfile" in error_msg:
                            flash('Error: The uploaded file appears to be corrupted or is not a valid Excel file. Please check your file and try again.')
                        elif "permission" in error_msg:
                            flash('Error: Permission denied while reading the file. Please close the file in Excel and try again.')
                        elif "unsupported format" in error_msg:
                            flash('Error: Unsupported file format. Please save your file as .xlsx or .xls and try again.')
                        else:
                            flash(f'Error reading Excel file: Please ensure your file is a valid Excel format (.xlsx or .xls). Technical details: {str(excel_error)[:100]}')
                        return render_template('admin_bulk_ebooks.html')

                required_columns = ['access_no', 'title', 'author', 'publisher', 'subject', 'department', 'category', 'file_format']
                missing_columns = [col for col in required_columns if col not in df.columns]

                if missing_columns:
                    flash(f'Missing required columns: {", ".join(missing_columns)}')
                    return render_template('admin_bulk_ebooks.html')

                success_count = 0
                error_count = 0
                errors = []

                for index, row in df.iterrows():
                    try:
                        # Check if access number already exists
                        if EBook.query.filter_by(access_no=row['access_no']).first():
                            error_count += 1
                            errors.append(f'Row {index + 2}: Access number {row["access_no"]} already exists')
                            continue

                        ebook = EBook(
                            access_no=row['access_no'],
                            title=row['title'],
                            author=row['author'],
                            publisher=row['publisher'],
                            subject=row['subject'],
                            department=row['department'],
                            category=row['category'],
                            file_format=row['file_format'],
                            file_size=row.get('file_size', ''),
                            download_url=row.get('download_url', ''),
                            isbn=row.get('isbn', ''),
                            pages=int(row['pages']) if pd.notna(row.get('pages')) else None,
                            language=row.get('language', 'English'),
                            description=row.get('description', '')
                        )

                        db.session.add(ebook)
                        success_count += 1

                    except Exception as e:
                        error_count += 1
                        errors.append(f'Row {index + 2}: {str(e)}')

                if success_count > 0:
                    db.session.commit()
                    flash(f'Successfully added {success_count} e-books!')

                if error_count > 0:
                    flash(f'{error_count} e-books failed to upload. Errors: {"; ".join(errors[:5])}{"..." if len(errors) > 5 else ""}')

                return redirect(url_for('admin_ebooks'))

            except Exception as e:
                flash(f'Error processing file: {str(e)}')
                return render_template('admin_bulk_ebooks.html')
        else:
            flash('Invalid file format. Please upload an Excel file.')

    return render_template('admin_bulk_ebooks.html', departments=get_departments_for_dropdown(), colleges=get_colleges_for_dropdown())

@app.route('/admin/bulk_users', methods=['GET', 'POST'])
def admin_bulk_users():
    if session.get('user_role') != 'admin':
        return redirect(url_for('login'))
    
    if request.method == 'POST':
        # Check if file was uploaded
        if 'file' not in request.files:
            flash('No file selected!')
            return redirect(request.url)
        
        file = request.files['file']
        user_type = request.form.get('user_type')
        
        if file.filename == '':
            flash('No file selected!')
            return redirect(request.url)
        
        if not user_type or user_type not in ['librarian', 'student']:
            flash('Please select a valid user type!')
            return redirect(request.url)
        
        if file and allowed_file(file.filename):
            filename = secure_filename(file.filename)
            filepath = os.path.join(app.config['UPLOAD_FOLDER'], filename)

            # Save file and verify it exists
            try:
                file.save(filepath)
                if not os.path.exists(filepath) or os.path.getsize(filepath) == 0:
                    flash('Error: File upload failed or file is empty. Please try again.')
                    return redirect(request.url)
            except Exception as save_error:
                flash(f'Error saving file: {str(save_error)}. Please try again.')
                return redirect(request.url)

            try:
                # Read Excel file with comprehensive error handling
                try:
                    # First, verify the file is a valid Excel file by checking its structure
                    df = pd.read_excel(filepath, engine='openpyxl')
                except Exception as excel_error:
                    error_msg = str(excel_error).lower()

                    # Try with xlrd engine for older .xls files
                    if filepath.endswith('.xls'):
                        try:
                            df = pd.read_excel(filepath, engine='xlrd')
                        except Exception as xlrd_error:
                            os.remove(filepath)
                            flash('Error: Unable to read the .xls file. Please save your file as .xlsx format and try again.')
                            return redirect(request.url)
                    else:
                        # Handle specific error types for .xlsx files
                        os.remove(filepath)
                        if "not a zip file" in error_msg or "bad zipfile" in error_msg:
                            flash('Error: The uploaded file appears to be corrupted or is not a valid Excel file. Please check your file and try again.')
                        elif "no such file" in error_msg:
                            flash('Error: File upload failed. Please try uploading the file again.')
                        elif "permission" in error_msg:
                            flash('Error: Permission denied while reading the file. Please close the file in Excel and try again.')
                        elif "unsupported format" in error_msg:
                            flash('Error: Unsupported file format. Please save your file as .xlsx or .xls and try again.')
                        else:
                            flash(f'Error reading Excel file: Please ensure your file is a valid Excel format (.xlsx or .xls). Technical details: {str(excel_error)[:100]}')
                        return redirect(request.url)
                
                # Get college and department from form
                college_id = request.form.get('college') if user_type == 'student' else None
                department_code = request.form.get('department') if user_type == 'student' else None

                # Validate college and department for students
                if user_type == 'student':
                    if not college_id or not department_code:
                        flash('Please select both college and department for student uploads.')
                        os.remove(filepath)
                        return redirect(request.url)

                # Validate required columns
                if user_type == 'student':
                    required_columns = ['user_id', 'full_name', 'email', 'designation', 'validity_date']
                else:
                    required_columns = ['name', 'user_id', 'email']
                
                if not all(col in df.columns for col in required_columns):
                    flash(f'Excel file must contain columns: {", ".join(required_columns)}')
                    os.remove(filepath)
                    return redirect(request.url)
                
                created_users = []
                errors = []
                
                for index, row in df.iterrows():
                    try:
                        # Handle both 'name' and 'full_name' columns for backward compatibility
                        name = str(row.get('full_name', row.get('name', ''))).strip()
                        user_id = str(row['user_id']).strip()
                        email = str(row['email']).strip() if pd.notna(row['email']) else None
                        
                        # Generate username and password
                        generated_email, password = generate_username_password(name, user_id, user_type)
                        
                        # Use provided email or generated one
                        final_email = email if email else generated_email
                        
                        # Check if user already exists
                        if user_type == 'librarian':
                            existing_user = Librarian.query.filter_by(email=final_email).first()
                        else:
                            existing_user = Student.query.filter_by(email=final_email).first() or \
                                          Student.query.filter_by(roll_number=user_id).first() or \
                                          Student.query.filter_by(user_id=str(row['user_id']).strip()).first()
                        
                        if existing_user:
                            errors.append(f"Row {index + 2}: User already exists (duplicate user_id, email, or roll_number)")
                            continue
                        
                        # Create new user
                        if user_type == 'librarian':
                            new_user = Librarian(
                                name=name,
                                email=final_email,
                                password=generate_password_hash(password)
                            )
                        else:
                            # Parse date fields for student
                            try:
                                validity_date = pd.to_datetime(row['validity_date']).date()

                                # Check if validity date is in the future
                                if validity_date <= date.today():
                                    errors.append(f"Row {index + 2}: Validity date must be in the future")
                                    continue

                                # Get department information
                                department = Department.query.filter_by(
                                    college_id=college_id,
                                    code=department_code,
                                    is_active=True
                                ).first()

                                # Auto-generate course based on department
                                course = f"B.Tech {department.name}" if department else "B.Tech"

                                # Set default DOB for staff or calculate for students
                                designation = str(row['designation']).strip()
                                if designation == 'Staff':
                                    dob = date(1990, 1, 1)  # Default DOB for staff
                                    current_year = None
                                else:
                                    dob = date(2003, 1, 1)  # Default DOB for students
                                    current_year = 1  # Default to first year

                                new_user = Student(
                                    user_id=str(row['user_id']).strip(),
                                    username=None,  # No longer using username - user_id is used for login
                                    name=name,
                                    roll_number=user_id,
                                    email=final_email,
                                    password=generate_password_hash(password),
                                    college_id=int(college_id) if college_id else None,
                                    department_id=department.id if department else None,
                                    department=department_code,  # Keep for backward compatibility
                                    college=College.query.get(college_id).name if college_id else None,  # Keep for backward compatibility
                                    designation=designation,
                                    course=course,
                                    dob=dob,
                                    current_year=current_year,
                                    validity_date=validity_date
                                )
                            except (ValueError, TypeError) as date_error:
                                errors.append(f"Row {index + 2}: Invalid date format or current_year - {str(date_error)}")
                                continue
                        
                        db.session.add(new_user)
                        created_users.append({
                            'name': name,
                            'email': final_email,
                            'password': password,
                            'user_id': user_id if user_type == 'student' else 'N/A',
                            'type': user_type,
                            'college': College.query.get(college_id).name if college_id and user_type == 'student' else 'N/A',
                            'department': department.name if user_type == 'student' and department else 'N/A',
                            'designation': designation if user_type == 'student' else 'Librarian'
                        })
                        
                    except Exception as e:
                        errors.append(f"Row {index + 2}: {str(e)}")
                
                # Commit all changes
                db.session.commit()
                
                # Clean up uploaded file
                os.remove(filepath)
                
                # Show results
                session['bulk_results'] = {
                    'created_users': created_users,
                    'errors': errors,
                    'user_type': user_type
                }
                
                flash(f'Bulk creation completed! {len(created_users)} {user_type}(s) created successfully.')
                return redirect(url_for('admin_bulk_results'))
                
            except Exception as e:
                # Clean up uploaded file
                if os.path.exists(filepath):
                    os.remove(filepath)
                flash(f'Error processing file: {str(e)}')
                return redirect(request.url)
        else:
            flash('Invalid file type. Please upload an Excel file (.xlsx or .xls)')
    
    return render_template('admin_bulk_users.html', colleges=get_colleges_for_dropdown())

@app.route('/admin/bulk_results')
def admin_bulk_results():
    if session.get('user_role') != 'admin':
        return redirect(url_for('login'))
    
    results = session.get('bulk_results')
    if not results:
        return redirect(url_for('admin_bulk_users'))
    
    return render_template('admin_bulk_results.html', results=results)

@app.route('/admin/download_credentials')
def admin_download_credentials():
    if session.get('user_role') != 'admin':
        return redirect(url_for('login'))

    results = session.get('bulk_results')
    if not results or not results.get('created_users'):
        flash('No credentials available for download.')
        return redirect(url_for('admin_bulk_users'))

    # Create credentials Excel file
    credentials_data = []
    for user in results['created_users']:
        credentials_data.append({
            'User ID': user['user_id'],
            'Full Name': user['name'],
            'Email': user['email'],
            'Password': user['password'],
            'Type': user['type'].title(),
            'College': user['college'],
            'Department': user['department'],
            'Designation': user['designation']
        })

    df = pd.DataFrame(credentials_data)

    # Create Excel file in memory
    output = BytesIO()
    with pd.ExcelWriter(output, engine='openpyxl') as writer:
        df.to_excel(writer, index=False, sheet_name='User Credentials')

        # Format the Excel file
        workbook = writer.book
        worksheet = writer.sheets['User Credentials']

        # Auto-adjust column widths
        for column in worksheet.columns:
            max_length = 0
            column_letter = column[0].column_letter
            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
            adjusted_width = min(max_length + 2, 50)
            worksheet.column_dimensions[column_letter].width = adjusted_width

    output.seek(0)

    # Generate filename with timestamp
    from datetime import datetime
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    filename = f'user_credentials_{timestamp}.xlsx'

    return send_file(
        output,
        mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        as_attachment=True,
        download_name=filename
    )

@app.route('/admin/download_template/<user_type>')
def admin_download_template(user_type):
    if session.get('user_role') != 'admin':
        return redirect(url_for('login'))
    
    if user_type not in ['librarian', 'student']:
        flash('Invalid user type!')
        return redirect(url_for('admin_bulk_users'))
    
    # Create sample data
    if user_type == 'librarian':
        data = {
            'name': ['John Doe', 'Jane Smith', 'Mike Johnson'],
            'roll_number': ['LIB001', 'LIB002', 'LIB003'],
            'email': ['<EMAIL>', '<EMAIL>', '']  # Third one will be auto-generated
        }
    else:
        data = {
            'user_id': ['STU001', 'STU002', 'STU003'],
            'full_name': ['Alice Brown', 'Bob Wilson', 'Carol Davis'],
            'email': ['<EMAIL>', '<EMAIL>', '<EMAIL>'],
            'designation': ['Student', 'Student', 'Staff'],
            'validity_date': ['2026-05-14', '2026-05-14', '2030-12-31']
        }
    
    # Create DataFrame and save to Excel
    df = pd.DataFrame(data)
    template_filename = f'{user_type}_template.xlsx'
    template_path = os.path.join(app.config['UPLOAD_FOLDER'], template_filename)
    
    with pd.ExcelWriter(template_path, engine='openpyxl') as writer:
        df.to_excel(writer, index=False, sheet_name=f'{user_type.title()} Template')
    
    return redirect(f'/uploads/{template_filename}')

@app.route('/uploads/<filename>')
def uploaded_file(filename):
    """Serve uploaded files"""
    from flask import send_from_directory
    return send_from_directory(app.config['UPLOAD_FOLDER'], filename)



if __name__ == '__main__':
    init_db()
    # Start the background cleanup scheduler
    schedule_cleanup()
    print("✅ Background cleanup scheduler started")
    app.run(debug=True)
