{% extends "base.html" %}

{% block title %}Circulation Counter - Librarian{% endblock %}

{% block sidebar %}
{% include 'librarian_sidebar.html' %}
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2><i class="fas fa-desktop me-3"></i>Circulation Counter</h2>
    <div>
        <button class="btn btn-outline-primary btn-custom" onclick="clearAll()">
            <i class="fas fa-refresh me-2"></i>Clear All
        </button>
    </div>
</div>

<!-- Quick Action Tabs -->
<ul class="nav nav-tabs mb-4" id="counterTabs" role="tablist">
    <li class="nav-item" role="presentation">
        <button class="nav-link active" id="issue-tab" data-bs-toggle="tab" data-bs-target="#issue" type="button" role="tab">
            <i class="fas fa-arrow-up me-2"></i>Issue Book
        </button>
    </li>
    <li class="nav-item" role="presentation">
        <button class="nav-link" id="return-tab" data-bs-toggle="tab" data-bs-target="#return" type="button" role="tab">
            <i class="fas fa-arrow-down me-2"></i>Return Book
        </button>
    </li>
    <li class="nav-item" role="presentation">
        <button class="nav-link" id="renew-tab" data-bs-toggle="tab" data-bs-target="#renew" type="button" role="tab">
            <i class="fas fa-redo me-2"></i>Renew Book
        </button>
    </li>
    <li class="nav-item" role="presentation">
        <button class="nav-link" id="reserve-tab" data-bs-toggle="tab" data-bs-target="#reserve" type="button" role="tab">
            <i class="fas fa-bookmark me-2"></i>Reserve Book
        </button>
    </li>
</ul>

<div class="tab-content" id="counterTabContent">
    <!-- Issue Book Tab -->
    <div class="tab-pane fade show active" id="issue" role="tabpanel">
        <div class="row">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-arrow-up me-2"></i>Issue Book</h5>
                    </div>
                    <div class="card-body">
                        <form id="issueForm">
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label for="studentSearch" class="form-label">Student ID/Name</label>
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="fas fa-user"></i></span>
                                        <input type="text" class="form-control" id="studentSearch" placeholder="Scan or type student ID">
                                        <button type="button" class="btn btn-outline-secondary" onclick="searchStudent()">
                                            <i class="fas fa-search"></i>
                                        </button>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <label for="bookSearch" class="form-label">Book Access Number/Title</label>
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="fas fa-book"></i></span>
                                        <input type="text" class="form-control" id="bookSearch" placeholder="Scan or type book access number">
                                        <button type="button" class="btn btn-outline-secondary" onclick="searchBook()">
                                            <i class="fas fa-search"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                            
                            <div id="studentInfo" class="alert alert-info" style="display: none;">
                                <h6><i class="fas fa-user me-2"></i>Student Information</h6>
                                <div id="studentDetails"></div>
                            </div>
                            
                            <div id="bookInfo" class="alert alert-success" style="display: none;">
                                <h6><i class="fas fa-book me-2"></i>Book Information</h6>
                                <div id="bookDetails"></div>
                            </div>
                            
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label for="dueDate" class="form-label">Due Date</label>
                                    <input type="date" class="form-control" id="dueDate">
                                </div>
                                <div class="col-md-6">
                                    <label for="issueNotes" class="form-label">Notes (Optional)</label>
                                    <input type="text" class="form-control" id="issueNotes" placeholder="Any special notes">
                                </div>
                            </div>
                            
                            <div class="text-center">
                                <button type="submit" class="btn btn-primary btn-lg">
                                    <i class="fas fa-check me-2"></i>Issue Book
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-info-circle me-2"></i>Quick Info</h6>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <small class="text-muted">Current Date/Time</small>
                            <div id="currentDateTime" class="fw-bold"></div>
                        </div>
                        <div class="mb-3">
                            <small class="text-muted">Default Loan Period</small>
                            <div class="fw-bold">14 Days</div>
                        </div>
                        <div class="mb-3">
                            <small class="text-muted">Books Available</small>
                            <div class="fw-bold text-success" id="availableBooks">Loading...</div>
                        </div>
                        <div class="mb-3">
                            <small class="text-muted">Books Issued Today</small>
                            <div class="fw-bold text-primary" id="issuedToday">Loading...</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Return Book Tab -->
    <div class="tab-pane fade" id="return" role="tabpanel">
        <div class="row">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-arrow-down me-2"></i>Return Book</h5>
                    </div>
                    <div class="card-body">
                        <form id="returnForm">
                            <div class="row mb-3">
                                <div class="col-md-12">
                                    <label for="returnBookSearch" class="form-label">Book Access Number</label>
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="fas fa-book"></i></span>
                                        <input type="text" class="form-control" id="returnBookSearch" placeholder="Scan or type book access number">
                                        <button type="button" class="btn btn-outline-secondary" onclick="searchReturnBook()">
                                            <i class="fas fa-search"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                            
                            <div id="returnBookInfo" class="alert alert-warning" style="display: none;">
                                <h6><i class="fas fa-exclamation-triangle me-2"></i>Return Information</h6>
                                <div id="returnBookDetails"></div>
                            </div>
                            
                            <div id="fineInfo" class="alert alert-danger" style="display: none;">
                                <h6><i class="fas fa-money-bill-wave me-2"></i>Fine Information</h6>
                                <div id="fineDetails"></div>
                            </div>
                            
                            <div class="text-center">
                                <button type="submit" class="btn btn-success btn-lg">
                                    <i class="fas fa-check me-2"></i>Return Book
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-clock me-2"></i>Recent Returns</h6>
                    </div>
                    <div class="card-body">
                        <div id="recentReturns">Loading...</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Renew Book Tab -->
    <div class="tab-pane fade" id="renew" role="tabpanel">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-redo me-2"></i>Renew Book</h5>
            </div>
            <div class="card-body">
                <form id="renewForm">
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="renewStudentSearch" class="form-label">Student ID</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-user"></i></span>
                                <input type="text" class="form-control" id="renewStudentSearch" placeholder="Student ID">
                                <button type="button" class="btn btn-outline-secondary" onclick="searchRenewStudent()">
                                    <i class="fas fa-search"></i>
                                </button>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <label for="renewBookSearch" class="form-label">Book Access Number</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-book"></i></span>
                                <input type="text" class="form-control" id="renewBookSearch" placeholder="Book Access Number">
                                <button type="button" class="btn btn-outline-secondary" onclick="searchRenewBook()">
                                    <i class="fas fa-search"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <div id="renewInfo" class="alert alert-info" style="display: none;">
                        <h6><i class="fas fa-info-circle me-2"></i>Renewal Information</h6>
                        <div id="renewDetails"></div>
                    </div>
                    
                    <div class="text-center">
                        <button type="submit" class="btn btn-warning btn-lg">
                            <i class="fas fa-redo me-2"></i>Renew Book
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <!-- Reserve Book Tab -->
    <div class="tab-pane fade" id="reserve" role="tabpanel">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-bookmark me-2"></i>Reserve Book</h5>
            </div>
            <div class="card-body">
                <form id="reserveForm">
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="reserveStudentSearch" class="form-label">Student ID</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-user"></i></span>
                                <input type="text" class="form-control" id="reserveStudentSearch" placeholder="Student ID">
                                <button type="button" class="btn btn-outline-secondary" onclick="searchReserveStudent()">
                                    <i class="fas fa-search"></i>
                                </button>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <label for="reserveBookSearch" class="form-label">Book Access Number/Title</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-book"></i></span>
                                <input type="text" class="form-control" id="reserveBookSearch" placeholder="Book Access Number or Title">
                                <button type="button" class="btn btn-outline-secondary" onclick="searchReserveBook()">
                                    <i class="fas fa-search"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <div id="reserveInfo" class="alert alert-warning" style="display: none;">
                        <h6><i class="fas fa-bookmark me-2"></i>Reservation Information</h6>
                        <div id="reserveDetails"></div>
                    </div>
                    
                    <div class="text-center">
                        <button type="submit" class="btn btn-info btn-lg">
                            <i class="fas fa-bookmark me-2"></i>Reserve Book
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Update current date/time
    function updateDateTime() {
        const now = new Date();
        $('#currentDateTime').text(now.toLocaleString());
    }
    updateDateTime();
    setInterval(updateDateTime, 1000);
    
    // Set default due date (14 days from now)
    const defaultDueDate = new Date();
    defaultDueDate.setDate(defaultDueDate.getDate() + 14);
    $('#dueDate').val(defaultDueDate.toISOString().split('T')[0]);
    
    // Load quick stats
    loadQuickStats();
});

function loadQuickStats() {
    // Load real statistics from API
    $.ajax({
        url: '/api/librarian/quick-stats',
        method: 'GET',
        success: function(response) {
            $('#availableBooks').text(response.available_books || '0');
            $('#issuedToday').text(response.issued_today || '0');
        },
        error: function() {
            $('#availableBooks').text('Error');
            $('#issuedToday').text('Error');
        }
    });
}

function clearAll() {
    $('form')[0].reset();
    $('.alert').hide();
    $('#dueDate').val(new Date(Date.now() + 14*24*60*60*1000).toISOString().split('T')[0]);
}

function searchStudent() {
    const query = $('#studentSearch').val();
    if (query) {
        $.ajax({
            url: '/api/librarian/search-student',
            method: 'GET',
            data: { query: query },
            success: function(response) {
                if (response.student) {
                    $('#studentInfo').show();
                    $('#studentDetails').html(`<strong>${response.student.name}</strong><br>ID: ${response.student.user_id}<br>Department: ${response.student.department}<br>Active Books: ${response.student.active_books || 0}`);
                } else {
                    $('#studentInfo').hide();
                    alert('Student not found');
                }
            },
            error: function() {
                $('#studentInfo').hide();
                alert('Error searching for student');
            }
        });
    }
}

function searchBook() {
    const query = $('#bookSearch').val();
    if (query) {
        // API call to search book
        $('#bookInfo').show();
        $('#bookDetails').html('<strong>Sample Book Title</strong><br>Access No: ' + query + '<br>Author: Sample Author<br>Available: Yes');
    }
}

function searchReturnBook() {
    const query = $('#returnBookSearch').val();
    if (query) {
        // API call to search return book
        $('#returnBookInfo').show();
        $('#returnBookDetails').html('<strong>Sample Book Title</strong><br>Issued to: John Doe<br>Issue Date: 2024-01-01<br>Due Date: 2024-01-15');
        
        // Check for fines
        const dueDate = new Date('2024-01-15');
        const today = new Date();
        if (today > dueDate) {
            const daysOverdue = Math.ceil((today - dueDate) / (1000 * 60 * 60 * 24));
            const fine = daysOverdue * 2; // ₹2 per day
            $('#fineInfo').show();
            $('#fineDetails').html('<strong>Overdue Fine: ₹' + fine + '</strong><br>Days Overdue: ' + daysOverdue);
        }
    }
}

// Similar functions for renew and reserve...
</script>
{% endblock %}
