{% extends "base.html" %}

{% block title %}Member Report - Librarian{% endblock %}

{% block sidebar %}
{% include 'librarian_sidebar.html' %}
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h2><i class="fas fa-users me-3 text-primary"></i>Member Report</h2>
        <p class="text-muted mb-0">Comprehensive member statistics and activity reports</p>
    </div>
    <div>
        <button class="btn btn-success" onclick="exportReport('excel')">
            <i class="fas fa-file-excel me-2"></i>Export Excel
        </button>
        <button class="btn btn-danger ms-2" onclick="exportReport('pdf')">
            <i class="fas fa-file-pdf me-2"></i>Export PDF
        </button>
    </div>
</div>

<!-- Filter Section -->
<div class="card mb-4">
    <div class="card-header">
        <h5 class="mb-0"><i class="fas fa-filter me-2"></i>Filters</h5>
    </div>
    <div class="card-body">
        <form id="filterForm" class="row g-3">
            <div class="col-md-3">
                <label for="dateFrom" class="form-label">From Date</label>
                <input type="date" class="form-control" id="dateFrom" name="dateFrom">
            </div>
            <div class="col-md-3">
                <label for="dateTo" class="form-label">To Date</label>
                <input type="date" class="form-control" id="dateTo" name="dateTo">
            </div>
            <div class="col-md-3">
                <label for="department" class="form-label">Department</label>
                <select class="form-select" id="department" name="department">
                    <option value="">All Departments</option>
                    <option value="CSE">Computer Science</option>
                    <option value="ECE">Electronics</option>
                    <option value="MECH">Mechanical</option>
                    <option value="CIVIL">Civil</option>
                </select>
            </div>
            <div class="col-md-3">
                <label for="memberType" class="form-label">Member Type</label>
                <select class="form-select" id="memberType" name="memberType">
                    <option value="">All Types</option>
                    <option value="student">Student</option>
                    <option value="faculty">Faculty</option>
                    <option value="staff">Staff</option>
                </select>
            </div>
            <div class="col-12">
                <button type="button" class="btn btn-primary" onclick="applyFilters()">
                    <i class="fas fa-search me-2"></i>Apply Filters
                </button>
                <button type="button" class="btn btn-secondary ms-2" onclick="resetFilters()">
                    <i class="fas fa-undo me-2"></i>Reset
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">Total Members</h6>
                        <h3 class="mb-0" id="totalMembers">0</h3>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-users fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">Active Members</h6>
                        <h3 class="mb-0" id="activeMembers">0</h3>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-user-check fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">New This Month</h6>
                        <h3 class="mb-0" id="newMembers">0</h3>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-user-plus fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">Books Issued</h6>
                        <h3 class="mb-0" id="booksIssued">0</h3>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-book fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Members Table -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0"><i class="fas fa-table me-2"></i>Member Details</h5>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-striped table-hover" id="memberTable">
                <thead class="table-dark">
                    <tr>
                        <th>Member ID</th>
                        <th>Name</th>
                        <th>Department</th>
                        <th>Type</th>
                        <th>Join Date</th>
                        <th>Books Issued</th>
                        <th>Last Activity</th>
                        <th>Status</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody id="memberTableBody">
                    <!-- Data will be loaded here -->
                </tbody>
            </table>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    loadMemberData();
});

function loadMemberData() {
    // Show loading state
    $('#memberTableBody').html('<tr><td colspan="9" class="text-center"><i class="fas fa-spinner fa-spin"></i> Loading...</td></tr>');
    
    // Simulate API call with mock data
    setTimeout(function() {
        const mockData = {
            statistics: {
                totalMembers: 1250,
                activeMembers: 1180,
                newMembers: 45,
                booksIssued: 2340
            },
            data: [
                {
                    id: 'STU001',
                    name: 'John Doe',
                    department: 'CSE',
                    type: 'Student',
                    joinDate: '2024-01-15',
                    booksIssued: 3,
                    lastActivity: '2024-01-20',
                    status: 'Active'
                },
                {
                    id: 'FAC001',
                    name: 'Dr. Jane Smith',
                    department: 'ECE',
                    type: 'Faculty',
                    joinDate: '2023-08-01',
                    booksIssued: 5,
                    lastActivity: '2024-01-19',
                    status: 'Active'
                }
            ]
        };
        
        updateStatistics(mockData.statistics);
        populateTable(mockData.data);
    }, 1000);
}

function updateStatistics(stats) {
    $('#totalMembers').text(stats.totalMembers || 0);
    $('#activeMembers').text(stats.activeMembers || 0);
    $('#newMembers').text(stats.newMembers || 0);
    $('#booksIssued').text(stats.booksIssued || 0);
}

function populateTable(data) {
    const tbody = $('#memberTableBody');
    tbody.empty();
    
    if (data.length === 0) {
        tbody.html('<tr><td colspan="9" class="text-center">No members found</td></tr>');
        return;
    }
    
    data.forEach(function(member) {
        const row = `
            <tr>
                <td><strong>${member.id}</strong></td>
                <td>${member.name}</td>
                <td>${member.department}</td>
                <td><span class="badge bg-${getTypeColor(member.type)}">${member.type}</span></td>
                <td>${member.joinDate}</td>
                <td>${member.booksIssued}</td>
                <td>${member.lastActivity}</td>
                <td><span class="badge bg-${getStatusColor(member.status)}">${member.status}</span></td>
                <td>
                    <button class="btn btn-sm btn-outline-primary" onclick="viewMember('${member.id}')">
                        <i class="fas fa-eye"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-success" onclick="editMember('${member.id}')">
                        <i class="fas fa-edit"></i>
                    </button>
                </td>
            </tr>
        `;
        tbody.append(row);
    });
}

function getTypeColor(type) {
    const colors = {
        'Student': 'primary',
        'Faculty': 'success',
        'Staff': 'info'
    };
    return colors[type] || 'secondary';
}

function getStatusColor(status) {
    const colors = {
        'Active': 'success',
        'Inactive': 'danger',
        'Suspended': 'warning'
    };
    return colors[status] || 'secondary';
}

function applyFilters() {
    loadMemberData();
}

function resetFilters() {
    $('#filterForm')[0].reset();
    loadMemberData();
}

function exportReport(format) {
    alert('Export functionality would be implemented here for format: ' + format);
}

function viewMember(memberId) {
    alert('View member details: ' + memberId);
}

function editMember(memberId) {
    alert('Edit member: ' + memberId);
}
</script>
{% endblock %}
