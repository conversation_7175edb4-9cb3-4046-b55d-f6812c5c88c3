{% extends "base.html" %}

{% block title %}Inventory Check - Librarian{% endblock %}

{% block sidebar %}
{% include 'librarian_sidebar.html' %}
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h2><i class="fas fa-clipboard-check me-3 text-info"></i>Inventory Check</h2>
        <p class="text-muted mb-0">Perform physical inventory verification and stock auditing</p>
    </div>
    <div>
        <button class="btn btn-success" onclick="exportInventoryReport()">
            <i class="fas fa-file-excel me-2"></i>Export Report
        </button>
    </div>
</div>

<!-- Inventory Session -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-play me-2"></i>Inventory Session</h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <label for="sessionName" class="form-label">Session Name</label>
                    <input type="text" class="form-control" id="sessionName" placeholder="e.g., Monthly Inventory - January 2024">
                </div>
                <div class="mb-3">
                    <label for="section" class="form-label">Section/Area</label>
                    <select class="form-select" id="section">
                        <option value="">Select Section</option>
                        <option value="A1">Section A1 - Computer Science</option>
                        <option value="A2">Section A2 - Electronics</option>
                        <option value="B1">Section B1 - Mechanical</option>
                        <option value="B2">Section B2 - Civil</option>
                        <option value="C1">Section C1 - Reference</option>
                        <option value="C2">Section C2 - Fiction</option>
                    </select>
                </div>
                <div class="d-grid gap-2">
                    <button class="btn btn-primary" onclick="startInventorySession()">
                        <i class="fas fa-play me-2"></i>Start New Session
                    </button>
                    <button class="btn btn-outline-secondary" onclick="resumeSession()">
                        <i class="fas fa-redo me-2"></i>Resume Previous Session
                    </button>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-chart-pie me-2"></i>Current Session Stats</h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6">
                        <div class="border-end">
                            <h3 class="text-success mb-1" id="scannedCount">0</h3>
                            <small class="text-muted">Scanned</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <h3 class="text-warning mb-1" id="missingCount">0</h3>
                        <small class="text-muted">Missing</small>
                    </div>
                </div>
                <hr>
                <div class="row text-center">
                    <div class="col-6">
                        <div class="border-end">
                            <h4 class="text-info mb-1" id="totalExpected">0</h4>
                            <small class="text-muted">Expected</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <h4 class="text-primary mb-1" id="completionRate">0%</h4>
                        <small class="text-muted">Complete</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Scanner Interface -->
<div class="card mb-4" id="scannerInterface" style="display: none;">
    <div class="card-header">
        <h5 class="mb-0"><i class="fas fa-barcode me-2"></i>Book Scanner</h5>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-8">
                <div class="input-group input-group-lg">
                    <span class="input-group-text"><i class="fas fa-barcode"></i></span>
                    <input type="text" class="form-control" id="bookScanner" placeholder="Scan or enter book access number" autofocus>
                    <button class="btn btn-primary" type="button" onclick="scanBook()">
                        <i class="fas fa-plus"></i> Add
                    </button>
                </div>
                <small class="text-muted">Scan books one by one or enter access numbers manually</small>
            </div>
            <div class="col-md-4">
                <div class="d-grid gap-2">
                    <button class="btn btn-success" onclick="markAsFound()">
                        <i class="fas fa-check me-2"></i>Mark as Found
                    </button>
                    <button class="btn btn-warning" onclick="markAsMissing()">
                        <i class="fas fa-exclamation-triangle me-2"></i>Mark as Missing
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Scanned Books List -->
<div class="card mb-4" id="scannedBooksCard" style="display: none;">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0"><i class="fas fa-list me-2"></i>Scanned Books</h5>
        <div>
            <button class="btn btn-sm btn-outline-primary" onclick="clearSession()">
                <i class="fas fa-trash me-2"></i>Clear Session
            </button>
            <button class="btn btn-sm btn-success" onclick="saveSession()">
                <i class="fas fa-save me-2"></i>Save Session
            </button>
        </div>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-striped table-hover">
                <thead class="table-dark">
                    <tr>
                        <th>Access No.</th>
                        <th>Title</th>
                        <th>Author</th>
                        <th>Status</th>
                        <th>Scan Time</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody id="scannedBooksBody">
                    <!-- Scanned books will appear here -->
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Missing Books -->
<div class="card" id="missingBooksCard" style="display: none;">
    <div class="card-header">
        <h5 class="mb-0 text-warning"><i class="fas fa-exclamation-triangle me-2"></i>Missing Books</h5>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-striped table-hover">
                <thead class="table-dark">
                    <tr>
                        <th>Access No.</th>
                        <th>Title</th>
                        <th>Author</th>
                        <th>Last Seen</th>
                        <th>Expected Location</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody id="missingBooksBody">
                    <!-- Missing books will appear here -->
                </tbody>
            </table>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let currentSession = null;
let scannedBooks = [];
let missingBooks = [];
let expectedBooks = [];

$(document).ready(function() {
    // Auto-focus on scanner input when it's visible
    $('#bookScanner').on('keypress', function(e) {
        if (e.which === 13) { // Enter key
            scanBook();
        }
    });
});

function startInventorySession() {
    const sessionName = $('#sessionName').val().trim();
    const section = $('#section').val();
    
    if (!sessionName || !section) {
        alert('Please enter session name and select a section');
        return;
    }
    
    currentSession = {
        name: sessionName,
        section: section,
        startTime: new Date(),
        scannedBooks: [],
        missingBooks: []
    };
    
    // Load expected books for the section
    loadExpectedBooks(section);
    
    // Show scanner interface
    $('#scannerInterface, #scannedBooksCard, #missingBooksCard').show();
    $('#bookScanner').focus();
    
    updateStats();
    
    alert(`Inventory session "${sessionName}" started for ${section}`);
}

function loadExpectedBooks(section) {
    // Simulate loading expected books for the section
    expectedBooks = [
        { accessNo: 'CS001', title: 'Introduction to Computer Science', author: 'John Smith' },
        { accessNo: 'CS002', title: 'Data Structures', author: 'Jane Doe' },
        { accessNo: 'CS003', title: 'Algorithms', author: 'Bob Johnson' }
    ];
    
    updateStats();
}

function scanBook() {
    const accessNo = $('#bookScanner').val().trim();
    if (!accessNo) return;
    
    // Check if already scanned
    if (scannedBooks.find(book => book.accessNo === accessNo)) {
        alert('Book already scanned!');
        $('#bookScanner').val('').focus();
        return;
    }
    
    // Find book in expected list
    const expectedBook = expectedBooks.find(book => book.accessNo === accessNo);
    
    if (expectedBook) {
        // Book found - add to scanned list
        const scannedBook = {
            ...expectedBook,
            status: 'Found',
            scanTime: new Date().toLocaleTimeString()
        };
        
        scannedBooks.push(scannedBook);
        addBookToTable(scannedBook, 'scannedBooksBody');
        
        // Remove from missing if it was there
        missingBooks = missingBooks.filter(book => book.accessNo !== accessNo);
        refreshMissingTable();
        
    } else {
        // Unexpected book
        const unexpectedBook = {
            accessNo: accessNo,
            title: 'Unknown Book',
            author: 'Unknown Author',
            status: 'Unexpected',
            scanTime: new Date().toLocaleTimeString()
        };
        
        scannedBooks.push(unexpectedBook);
        addBookToTable(unexpectedBook, 'scannedBooksBody');
    }
    
    $('#bookScanner').val('').focus();
    updateStats();
}

function addBookToTable(book, tableBodyId) {
    const statusBadge = getStatusBadge(book.status);
    const row = `
        <tr>
            <td><strong>${book.accessNo}</strong></td>
            <td>${book.title}</td>
            <td>${book.author}</td>
            <td>${statusBadge}</td>
            <td>${book.scanTime}</td>
            <td>
                <button class="btn btn-sm btn-outline-danger" onclick="removeScannedBook('${book.accessNo}')">
                    <i class="fas fa-trash"></i>
                </button>
            </td>
        </tr>
    `;
    $(`#${tableBodyId}`).append(row);
}

function getStatusBadge(status) {
    const badges = {
        'Found': '<span class="badge bg-success">Found</span>',
        'Missing': '<span class="badge bg-danger">Missing</span>',
        'Unexpected': '<span class="badge bg-warning">Unexpected</span>'
    };
    return badges[status] || '<span class="badge bg-secondary">Unknown</span>';
}

function markAsMissing() {
    const accessNo = $('#bookScanner').val().trim();
    if (!accessNo) {
        alert('Please enter an access number');
        return;
    }
    
    const expectedBook = expectedBooks.find(book => book.accessNo === accessNo);
    if (!expectedBook) {
        alert('Book not found in expected list');
        return;
    }
    
    // Check if already marked as missing
    if (missingBooks.find(book => book.accessNo === accessNo)) {
        alert('Book already marked as missing');
        return;
    }
    
    const missingBook = {
        ...expectedBook,
        lastSeen: 'Unknown',
        expectedLocation: currentSession.section
    };
    
    missingBooks.push(missingBook);
    addMissingBookToTable(missingBook);
    
    $('#bookScanner').val('').focus();
    updateStats();
}

function addMissingBookToTable(book) {
    const row = `
        <tr>
            <td><strong>${book.accessNo}</strong></td>
            <td>${book.title}</td>
            <td>${book.author}</td>
            <td>${book.lastSeen}</td>
            <td>${book.expectedLocation}</td>
            <td>
                <button class="btn btn-sm btn-outline-success" onclick="markAsFound('${book.accessNo}')">
                    <i class="fas fa-check"></i> Found
                </button>
            </td>
        </tr>
    `;
    $('#missingBooksBody').append(row);
}

function markAsFound(accessNo) {
    // Remove from missing and add to scanned
    const missingBook = missingBooks.find(book => book.accessNo === accessNo);
    if (missingBook) {
        missingBooks = missingBooks.filter(book => book.accessNo !== accessNo);
        
        const foundBook = {
            ...missingBook,
            status: 'Found',
            scanTime: new Date().toLocaleTimeString()
        };
        
        scannedBooks.push(foundBook);
        
        refreshTables();
        updateStats();
    }
}

function removeScannedBook(accessNo) {
    scannedBooks = scannedBooks.filter(book => book.accessNo !== accessNo);
    refreshTables();
    updateStats();
}

function refreshTables() {
    // Clear and repopulate tables
    $('#scannedBooksBody').empty();
    $('#missingBooksBody').empty();
    
    scannedBooks.forEach(book => addBookToTable(book, 'scannedBooksBody'));
    missingBooks.forEach(book => addMissingBookToTable(book));
}

function refreshMissingTable() {
    $('#missingBooksBody').empty();
    missingBooks.forEach(book => addMissingBookToTable(book));
}

function updateStats() {
    const scannedCount = scannedBooks.filter(book => book.status === 'Found').length;
    const missingCount = missingBooks.length;
    const totalExpected = expectedBooks.length;
    const completionRate = totalExpected > 0 ? Math.round((scannedCount / totalExpected) * 100) : 0;
    
    $('#scannedCount').text(scannedCount);
    $('#missingCount').text(missingCount);
    $('#totalExpected').text(totalExpected);
    $('#completionRate').text(completionRate + '%');
}

function clearSession() {
    if (confirm('Are you sure you want to clear the current session? All data will be lost.')) {
        scannedBooks = [];
        missingBooks = [];
        currentSession = null;
        
        $('#scannerInterface, #scannedBooksCard, #missingBooksCard').hide();
        $('#scannedBooksBody, #missingBooksBody').empty();
        $('#sessionName, #bookScanner').val('');
        $('#section').val('');
        
        updateStats();
    }
}

function saveSession() {
    if (!currentSession) {
        alert('No active session to save');
        return;
    }
    
    const sessionData = {
        ...currentSession,
        scannedBooks: scannedBooks,
        missingBooks: missingBooks,
        endTime: new Date(),
        stats: {
            scanned: scannedBooks.filter(book => book.status === 'Found').length,
            missing: missingBooks.length,
            unexpected: scannedBooks.filter(book => book.status === 'Unexpected').length,
            total: expectedBooks.length
        }
    };
    
    // In a real application, this would save to the database
    console.log('Session saved:', sessionData);
    alert('Inventory session saved successfully!');
}

function resumeSession() {
    alert('Resume session functionality would load a previously saved session');
}

function exportInventoryReport() {
    if (!currentSession) {
        alert('No active session to export');
        return;
    }
    
    alert('Export inventory report functionality would generate an Excel/PDF report');
}
</script>
{% endblock %}
