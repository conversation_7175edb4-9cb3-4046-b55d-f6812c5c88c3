{% extends "base.html" %}

{% block title %}News Clippings - Librarian{% endblock %}

{% block sidebar %}
{% include 'librarian_sidebar.html' %}
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h2><i class="fas fa-newspaper me-3 text-primary"></i>News Clippings Management</h2>
        <p class="text-muted mb-0">Manage and organize news clippings and media coverage</p>
    </div>
    <div>
        <a href="{{ url_for('librarian_add_news_clipping') }}" class="btn btn-primary">
            <i class="fas fa-plus me-2"></i>Add News Clipping
        </a>
        <button class="btn btn-success ms-2" onclick="exportClippings()">
            <i class="fas fa-file-excel me-2"></i>Export
        </button>
    </div>
</div>

<!-- Filter Section -->
<div class="card mb-4">
    <div class="card-header">
        <h5 class="mb-0"><i class="fas fa-filter me-2"></i>Filters</h5>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-3">
                <label for="dateFrom" class="form-label">From Date</label>
                <input type="date" class="form-control" id="dateFrom">
            </div>
            <div class="col-md-3">
                <label for="dateTo" class="form-label">To Date</label>
                <input type="date" class="form-control" id="dateTo">
            </div>
            <div class="col-md-3">
                <label for="newsType" class="form-label">News Type</label>
                <select class="form-select" id="newsType">
                    <option value="">All Types</option>
                    <option value="academic">Academic</option>
                    <option value="research">Research</option>
                    <option value="event">Event</option>
                    <option value="achievement">Achievement</option>
                    <option value="general">General</option>
                </select>
            </div>
            <div class="col-md-3">
                <label for="newspaper" class="form-label">Newspaper</label>
                <input type="text" class="form-control" id="newspaper" placeholder="Search by newspaper">
            </div>
        </div>
        <div class="row mt-3">
            <div class="col-md-6">
                <label for="keywords" class="form-label">Keywords</label>
                <input type="text" class="form-control" id="keywords" placeholder="Search by keywords">
            </div>
            <div class="col-md-6 d-flex align-items-end">
                <button class="btn btn-primary me-2" onclick="applyFilters()">
                    <i class="fas fa-search me-2"></i>Search
                </button>
                <button class="btn btn-secondary" onclick="resetFilters()">
                    <i class="fas fa-undo me-2"></i>Reset
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">Total Clippings</h6>
                        <h3 class="mb-0" id="totalClippings">0</h3>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-newspaper fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">This Month</h6>
                        <h3 class="mb-0" id="thisMonth">0</h3>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-calendar fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">Newspapers</h6>
                        <h3 class="mb-0" id="uniqueNewspapers">0</h3>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-building fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">Categories</h6>
                        <h3 class="mb-0" id="categories">0</h3>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-tags fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- News Clippings Table -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0"><i class="fas fa-table me-2"></i>News Clippings</h5>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-striped table-hover" id="clippingsTable">
                <thead class="table-dark">
                    <tr>
                        <th>Clipping No.</th>
                        <th>Date</th>
                        <th>Newspaper</th>
                        <th>Type</th>
                        <th>Keywords</th>
                        <th>Pages</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody id="clippingsTableBody">
                    <!-- Data will be loaded here -->
                </tbody>
            </table>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// Initialize the page
$(document).ready(function() {
    loadClippings();
    
    // Set default dates (last 30 days)
    const today = new Date();
    const lastMonth = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000);
    
    $('#dateTo').val(today.toISOString().split('T')[0]);
    $('#dateFrom').val(lastMonth.toISOString().split('T')[0]);
});

function loadClippings() {
    // Show loading state
    $('#clippingsTableBody').html('<tr><td colspan="7" class="text-center"><i class="fas fa-spinner fa-spin"></i> Loading...</td></tr>');
    
    // Simulate API call with mock data
    setTimeout(function() {
        const mockData = {
            statistics: {
                totalClippings: 45,
                thisMonth: 12,
                uniqueNewspapers: 8,
                categories: 5
            },
            data: [
                {
                    id: '1',
                    clipping_no: 'NC001',
                    date: '2024-01-15',
                    newspaper: 'Times of India',
                    type: 'academic',
                    keywords: 'education, technology',
                    pages: '2'
                },
                {
                    id: '2',
                    clipping_no: 'NC002',
                    date: '2024-01-14',
                    newspaper: 'Indian Express',
                    type: 'research',
                    keywords: 'research, innovation',
                    pages: '1'
                }
            ]
        };
        
        updateStatistics(mockData.statistics);
        populateTable(mockData.data);
    }, 1000);
}

function updateStatistics(stats) {
    $('#totalClippings').text(stats.totalClippings || 0);
    $('#thisMonth').text(stats.thisMonth || 0);
    $('#uniqueNewspapers').text(stats.uniqueNewspapers || 0);
    $('#categories').text(stats.categories || 0);
}

function populateTable(data) {
    const tbody = $('#clippingsTableBody');
    tbody.empty();
    
    if (data.length === 0) {
        tbody.html('<tr><td colspan="7" class="text-center">No clippings found</td></tr>');
        return;
    }
    
    data.forEach(function(clipping) {
        const row = `
            <tr>
                <td><strong>${clipping.clipping_no}</strong></td>
                <td>${clipping.date}</td>
                <td>${clipping.newspaper}</td>
                <td><span class="badge bg-${getTypeColor(clipping.type)}">${clipping.type}</span></td>
                <td>${clipping.keywords}</td>
                <td>${clipping.pages}</td>
                <td>
                    <button class="btn btn-sm btn-outline-primary" onclick="viewClipping('${clipping.id}')">
                        <i class="fas fa-eye"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-warning" onclick="editClipping('${clipping.id}')">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-danger" onclick="deleteClipping('${clipping.id}')">
                        <i class="fas fa-trash"></i>
                    </button>
                </td>
            </tr>
        `;
        tbody.append(row);
    });
}

function getTypeColor(type) {
    const colors = {
        'academic': 'primary',
        'research': 'success',
        'event': 'info',
        'achievement': 'warning',
        'general': 'secondary'
    };
    return colors[type] || 'secondary';
}

function applyFilters() {
    loadClippings();
}

function resetFilters() {
    $('#dateFrom').val('');
    $('#dateTo').val('');
    $('#newsType').val('');
    $('#newspaper').val('');
    $('#keywords').val('');
    loadClippings();
}

function exportClippings() {
    alert('Export functionality would be implemented here');
}

function viewClipping(id) {
    alert('View clipping: ' + id);
}

function editClipping(id) {
    alert('Edit clipping: ' + id);
}

function deleteClipping(id) {
    if (confirm('Are you sure you want to delete this clipping?')) {
        alert('Delete clipping: ' + id);
    }
}
</script>
{% endblock %}
