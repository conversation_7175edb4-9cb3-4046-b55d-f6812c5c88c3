{% extends "base.html" %}

{% block title %}Fine Management - Librarian{% endblock %}

{% block sidebar %}
{% include 'librarian_sidebar.html' %}
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h2><i class="fas fa-money-bill-wave me-3 text-warning"></i>Fine Management</h2>
        <p class="text-muted mb-0">Add and manage fines for library users</p>
    </div>
    <div>
        <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addFineModal">
            <i class="fas fa-plus me-2"></i>Add Fine
        </button>
        <button class="btn btn-success ms-2" onclick="exportFines()">
            <i class="fas fa-file-excel me-2"></i>Export
        </button>
    </div>
</div>

<!-- Quick Actions -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card autolib-card text-center">
            <div class="card-body">
                <div class="text-danger">
                    <i class="fas fa-money-bill-wave fa-2x mb-2"></i>
                </div>
                <h4 class="text-danger" id="totalPendingFines">₹0</h4>
                <p class="text-muted mb-0 small">Pending Fines</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card autolib-card text-center">
            <div class="card-body">
                <div class="text-success">
                    <i class="fas fa-check-circle fa-2x mb-2"></i>
                </div>
                <h4 class="text-success" id="todayCollection">₹0</h4>
                <p class="text-muted mb-0 small">Today's Collection</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card autolib-card text-center">
            <div class="card-body">
                <div class="text-warning">
                    <i class="fas fa-users fa-2x mb-2"></i>
                </div>
                <h4 class="text-warning" id="usersWithFines">0</h4>
                <p class="text-muted mb-0 small">Users with Fines</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card autolib-card text-center">
            <div class="card-body">
                <div class="text-info">
                    <i class="fas fa-clock fa-2x mb-2"></i>
                </div>
                <h4 class="text-info" id="overdueFines">0</h4>
                <p class="text-muted mb-0 small">Overdue Fines</p>
            </div>
        </div>
    </div>
</div>

<!-- Quick Fine Addition -->
<div class="card autolib-card mb-4">
    <div class="card-header bg-gradient-primary text-white">
        <h5 class="mb-0"><i class="fas fa-bolt me-2"></i>Quick Fine Addition</h5>
    </div>
    <div class="card-body">
        <form id="quickFineForm" class="row g-3">
            <div class="col-md-3">
                <label for="quickUserId" class="form-label">User ID</label>
                <input type="text" class="form-control" id="quickUserId" placeholder="Enter User ID">
            </div>
            <div class="col-md-2">
                <label for="quickFineType" class="form-label">Fine Type</label>
                <select class="form-select" id="quickFineType">
                    <option value="overdue">Overdue</option>
                    <option value="damage">Damage</option>
                    <option value="lost">Lost Book</option>
                    <option value="other">Other</option>
                </select>
            </div>
            <div class="col-md-2">
                <label for="quickAmount" class="form-label">Amount (₹)</label>
                <input type="number" class="form-control" id="quickAmount" min="1" step="0.01">
            </div>
            <div class="col-md-3">
                <label for="quickReason" class="form-label">Reason</label>
                <input type="text" class="form-control" id="quickReason" placeholder="Brief reason">
            </div>
            <div class="col-md-2">
                <label class="form-label">&nbsp;</label>
                <button type="button" class="btn btn-warning d-block w-100" onclick="addQuickFine()">
                    <i class="fas fa-plus me-2"></i>Add Fine
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Search and Filter -->
<div class="card autolib-card mb-4">
    <div class="card-header bg-gradient-info text-white">
        <h5 class="mb-0"><i class="fas fa-search me-2"></i>Search Fines</h5>
    </div>
    <div class="card-body">
        <form id="searchForm" class="row g-3">
            <div class="col-md-4">
                <label for="searchUserId" class="form-label">Search User</label>
                <input type="text" class="form-control" id="searchUserId" placeholder="User ID or Name">
            </div>
            <div class="col-md-3">
                <label for="statusFilter" class="form-label">Status</label>
                <select class="form-select" id="statusFilter">
                    <option value="">All Status</option>
                    <option value="pending">Pending</option>
                    <option value="paid">Paid</option>
                    <option value="waived">Waived</option>
                    <option value="overdue">Overdue</option>
                </select>
            </div>
            <div class="col-md-3">
                <label for="typeFilter" class="form-label">Fine Type</label>
                <select class="form-select" id="typeFilter">
                    <option value="">All Types</option>
                    <option value="overdue">Overdue Book</option>
                    <option value="damage">Book Damage</option>
                    <option value="lost">Lost Book</option>
                    <option value="other">Other</option>
                </select>
            </div>
            <div class="col-md-2">
                <label class="form-label">&nbsp;</label>
                <button type="button" class="btn btn-info d-block w-100" onclick="searchFines()">
                    <i class="fas fa-search me-2"></i>Search
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Fines Table -->
<div class="card autolib-card">
    <div class="card-header">
        <h5 class="mb-0"><i class="fas fa-table me-2"></i>Current Fines</h5>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-striped table-hover" id="finesTable">
                <thead class="table-dark">
                    <tr>
                        <th>Fine ID</th>
                        <th>User ID</th>
                        <th>Name</th>
                        <th>Type</th>
                        <th>Amount (₹)</th>
                        <th>Issue Date</th>
                        <th>Due Date</th>
                        <th>Status</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td colspan="9" class="text-center text-success">
                            <i class="fas fa-check-circle me-2"></i>No fines found. All users are clear!
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Add Fine Modal -->
<div class="modal fade" id="addFineModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-warning text-white">
                <h5 class="modal-title"><i class="fas fa-plus me-2"></i>Add New Fine</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="addFineForm">
                    <div class="row">
                        <div class="col-md-6">
                            <label for="modalUserId" class="form-label">User ID *</label>
                            <input type="text" class="form-control" id="modalUserId" required>
                            <div class="form-text">Enter student/user ID</div>
                        </div>
                        <div class="col-md-6">
                            <label for="modalUserName" class="form-label">User Name</label>
                            <input type="text" class="form-control" id="modalUserName" readonly>
                            <div class="form-text">Auto-filled from user ID</div>
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-md-6">
                            <label for="modalFineType" class="form-label">Fine Type *</label>
                            <select class="form-select" id="modalFineType" required>
                                <option value="">Select Fine Type</option>
                                <option value="overdue">Overdue Book</option>
                                <option value="damage">Book Damage</option>
                                <option value="lost">Lost Book</option>
                                <option value="late_return">Late Return</option>
                                <option value="other">Other</option>
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label for="modalFineAmount" class="form-label">Fine Amount (₹) *</label>
                            <input type="number" class="form-control" id="modalFineAmount" min="1" step="0.01" required>
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-md-6">
                            <label for="modalBookId" class="form-label">Book ID (if applicable)</label>
                            <input type="text" class="form-control" id="modalBookId">
                        </div>
                        <div class="col-md-6">
                            <label for="modalDueDate" class="form-label">Due Date *</label>
                            <input type="date" class="form-control" id="modalDueDate" required>
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-12">
                            <label for="modalFineReason" class="form-label">Reason/Description *</label>
                            <textarea class="form-control" id="modalFineReason" rows="3" required></textarea>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-warning" onclick="addDetailedFine()">
                    <i class="fas fa-plus me-2"></i>Add Fine
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    loadFines();
    
    // Set default due date (7 days from today)
    const defaultDueDate = new Date();
    defaultDueDate.setDate(defaultDueDate.getDate() + 7);
    $('#modalDueDate').val(defaultDueDate.toISOString().split('T')[0]);
    
    // Auto-fill user name when user ID is entered
    $('#modalUserId, #quickUserId').on('blur', function() {
        const userId = $(this).val();
        if (userId) {
            // Simulate user lookup - replace with actual API call
            if ($(this).attr('id') === 'modalUserId') {
                $('#modalUserName').val('Sample User Name');
            }
        }
    });
});

function loadFines() {
    // Show loading
    $('#finesTable tbody').html('<tr><td colspan="9" class="text-center"><i class="fas fa-spinner fa-spin me-2"></i>Loading fines...</td></tr>');
    
    // Update statistics - all zeros for clean database
    $('#totalPendingFines').text('₹0');
    $('#todayCollection').text('₹0');
    $('#usersWithFines').text('0');
    $('#overdueFines').text('0');
    
    // Simulate API call - replace with actual implementation
    setTimeout(() => {
        $('#finesTable tbody').html(`
            <tr>
                <td colspan="9" class="text-center text-success">
                    <i class="fas fa-check-circle me-2"></i>No fines found. All users are clear!
                </td>
            </tr>
        `);
    }, 1000);
}

function addQuickFine() {
    const userId = $('#quickUserId').val();
    const fineType = $('#quickFineType').val();
    const amount = $('#quickAmount').val();
    const reason = $('#quickReason').val();
    
    if (!userId || !amount || !reason) {
        alert('Please fill all required fields.');
        return;
    }
    
    // Simulate API call - replace with actual implementation
    alert('Fine added successfully!');
    $('#quickFineForm')[0].reset();
    loadFines();
}

function addDetailedFine() {
    const formData = {
        userId: $('#modalUserId').val(),
        fineType: $('#modalFineType').val(),
        fineAmount: $('#modalFineAmount').val(),
        bookId: $('#modalBookId').val(),
        dueDate: $('#modalDueDate').val(),
        reason: $('#modalFineReason').val()
    };
    
    // Validate required fields
    if (!formData.userId || !formData.fineType || !formData.fineAmount || !formData.dueDate || !formData.reason) {
        alert('Please fill all required fields.');
        return;
    }
    
    // Simulate API call - replace with actual implementation
    alert('Fine added successfully!');
    $('#addFineModal').modal('hide');
    $('#addFineForm')[0].reset();
    loadFines();
}

function searchFines() {
    const searchData = {
        userId: $('#searchUserId').val(),
        status: $('#statusFilter').val(),
        type: $('#typeFilter').val()
    };
    
    // Simulate search - replace with actual implementation
    loadFines();
}

function collectFine(fineId) {
    if (confirm('Mark this fine as collected?')) {
        alert('Fine collected successfully!');
        loadFines();
    }
}

function waiveFine(fineId) {
    if (confirm('Waive this fine? This action cannot be undone.')) {
        alert('Fine waived successfully!');
        loadFines();
    }
}

function exportFines() {
    alert('Exporting fines data...');
}
</script>
{% endblock %}
