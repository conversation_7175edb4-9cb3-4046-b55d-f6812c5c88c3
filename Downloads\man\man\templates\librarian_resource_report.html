{% extends "base.html" %}

{% block title %}Resource Report - Librarian{% endblock %}

{% block sidebar %}
{% include 'librarian_sidebar.html' %}
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h2><i class="fas fa-archive me-3 text-info"></i>Resource Report</h2>
        <p class="text-muted mb-0">Comprehensive library resource utilization and statistics</p>
    </div>
    <div>
        <button class="btn btn-success" onclick="exportReport('excel')">
            <i class="fas fa-file-excel me-2"></i>Export Excel
        </button>
        <button class="btn btn-danger ms-2" onclick="exportReport('pdf')">
            <i class="fas fa-file-pdf me-2"></i>Export PDF
        </button>
    </div>
</div>

<!-- Filter Section -->
<div class="card mb-4">
    <div class="card-header">
        <h5 class="mb-0"><i class="fas fa-filter me-2"></i>Filters</h5>
    </div>
    <div class="card-body">
        <form id="filterForm" class="row g-3">
            <div class="col-md-3">
                <label for="resourceType" class="form-label">Resource Type</label>
                <select class="form-select" id="resourceType" name="resourceType">
                    <option value="">All Resources</option>
                    <option value="books">Books</option>
                    <option value="ebooks">E-Books</option>
                    <option value="journals">Journals</option>
                    <option value="magazines">Magazines</option>
                </select>
            </div>
            <div class="col-md-3">
                <label for="department" class="form-label">Department</label>
                <select class="form-select" id="department" name="department">
                    <option value="">All Departments</option>
                    <option value="CSE">Computer Science</option>
                    <option value="ECE">Electronics</option>
                    <option value="MECH">Mechanical</option>
                    <option value="CIVIL">Civil</option>
                </select>
            </div>
            <div class="col-md-3">
                <label for="category" class="form-label">Category</label>
                <select class="form-select" id="category" name="category">
                    <option value="">All Categories</option>
                    <option value="textbook">Textbook</option>
                    <option value="reference">Reference</option>
                    <option value="fiction">Fiction</option>
                    <option value="research">Research</option>
                </select>
            </div>
            <div class="col-md-3">
                <label for="status" class="form-label">Status</label>
                <select class="form-select" id="status" name="status">
                    <option value="">All Status</option>
                    <option value="available">Available</option>
                    <option value="issued">Issued</option>
                    <option value="reserved">Reserved</option>
                    <option value="maintenance">Maintenance</option>
                </select>
            </div>
            <div class="col-12">
                <button type="button" class="btn btn-primary" onclick="applyFilters()">
                    <i class="fas fa-search me-2"></i>Apply Filters
                </button>
                <button type="button" class="btn btn-secondary ms-2" onclick="resetFilters()">
                    <i class="fas fa-undo me-2"></i>Reset
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">Total Resources</h6>
                        <h3 class="mb-0" id="totalResources">0</h3>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-archive fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">Available</h6>
                        <h3 class="mb-0" id="availableResources">0</h3>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-check-circle fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">Currently Issued</h6>
                        <h3 class="mb-0" id="issuedResources">0</h3>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-hand-holding fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">Utilization Rate</h6>
                        <h3 class="mb-0" id="utilizationRate">0%</h3>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-chart-line fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Resource Utilization Chart -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-chart-pie me-2"></i>Resource Distribution</h5>
            </div>
            <div class="card-body">
                <canvas id="resourceChart" width="400" height="200"></canvas>
            </div>
        </div>
    </div>
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-chart-bar me-2"></i>Department-wise Usage</h5>
            </div>
            <div class="card-body">
                <canvas id="departmentChart" width="400" height="200"></canvas>
            </div>
        </div>
    </div>
</div>

<!-- Resources Table -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0"><i class="fas fa-table me-2"></i>Resource Details</h5>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-striped table-hover" id="resourceTable">
                <thead class="table-dark">
                    <tr>
                        <th>Resource ID</th>
                        <th>Title</th>
                        <th>Type</th>
                        <th>Category</th>
                        <th>Department</th>
                        <th>Total Copies</th>
                        <th>Available</th>
                        <th>Issued</th>
                        <th>Utilization</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody id="resourceTableBody">
                    <!-- Data will be loaded here -->
                </tbody>
            </table>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
$(document).ready(function() {
    loadResourceData();
    initializeCharts();
});

function loadResourceData() {
    // Show loading state
    $('#resourceTableBody').html('<tr><td colspan="10" class="text-center"><i class="fas fa-spinner fa-spin"></i> Loading...</td></tr>');
    
    // Simulate API call with mock data
    setTimeout(function() {
        const mockData = {
            statistics: {
                totalResources: 5420,
                availableResources: 3890,
                issuedResources: 1530,
                utilizationRate: 28.2
            },
            data: [
                {
                    id: 'BK001',
                    title: 'Introduction to Computer Science',
                    type: 'Book',
                    category: 'Textbook',
                    department: 'CSE',
                    totalCopies: 10,
                    available: 7,
                    issued: 3,
                    utilization: 30
                },
                {
                    id: 'EB001',
                    title: 'Digital Electronics',
                    type: 'E-Book',
                    category: 'Reference',
                    department: 'ECE',
                    totalCopies: 1,
                    available: 1,
                    issued: 0,
                    utilization: 0
                }
            ]
        };
        
        updateStatistics(mockData.statistics);
        populateTable(mockData.data);
        updateCharts(mockData);
    }, 1000);
}

function updateStatistics(stats) {
    $('#totalResources').text(stats.totalResources || 0);
    $('#availableResources').text(stats.availableResources || 0);
    $('#issuedResources').text(stats.issuedResources || 0);
    $('#utilizationRate').text((stats.utilizationRate || 0) + '%');
}

function populateTable(data) {
    const tbody = $('#resourceTableBody');
    tbody.empty();
    
    if (data.length === 0) {
        tbody.html('<tr><td colspan="10" class="text-center">No resources found</td></tr>');
        return;
    }
    
    data.forEach(function(resource) {
        const row = `
            <tr>
                <td><strong>${resource.id}</strong></td>
                <td>${resource.title}</td>
                <td><span class="badge bg-${getTypeColor(resource.type)}">${resource.type}</span></td>
                <td>${resource.category}</td>
                <td>${resource.department}</td>
                <td>${resource.totalCopies}</td>
                <td>${resource.available}</td>
                <td>${resource.issued}</td>
                <td>
                    <div class="progress" style="height: 20px;">
                        <div class="progress-bar" role="progressbar" style="width: ${resource.utilization}%">
                            ${resource.utilization}%
                        </div>
                    </div>
                </td>
                <td>
                    <button class="btn btn-sm btn-outline-primary" onclick="viewResource('${resource.id}')">
                        <i class="fas fa-eye"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-success" onclick="editResource('${resource.id}')">
                        <i class="fas fa-edit"></i>
                    </button>
                </td>
            </tr>
        `;
        tbody.append(row);
    });
}

function getTypeColor(type) {
    const colors = {
        'Book': 'primary',
        'E-Book': 'success',
        'Journal': 'info',
        'Magazine': 'warning'
    };
    return colors[type] || 'secondary';
}

function initializeCharts() {
    // Resource Distribution Chart
    const resourceCtx = document.getElementById('resourceChart').getContext('2d');
    new Chart(resourceCtx, {
        type: 'pie',
        data: {
            labels: ['Books', 'E-Books', 'Journals', 'Magazines'],
            datasets: [{
                data: [3200, 1800, 300, 120],
                backgroundColor: ['#007bff', '#28a745', '#17a2b8', '#ffc107']
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false
        }
    });

    // Department Usage Chart
    const deptCtx = document.getElementById('departmentChart').getContext('2d');
    new Chart(deptCtx, {
        type: 'bar',
        data: {
            labels: ['CSE', 'ECE', 'MECH', 'CIVIL'],
            datasets: [{
                label: 'Resources Used',
                data: [450, 380, 320, 280],
                backgroundColor: '#007bff'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });
}

function updateCharts(data) {
    // Charts would be updated with real data here
}

function applyFilters() {
    loadResourceData();
}

function resetFilters() {
    $('#filterForm')[0].reset();
    loadResourceData();
}

function exportReport(format) {
    alert('Export functionality would be implemented here for format: ' + format);
}

function viewResource(resourceId) {
    alert('View resource details: ' + resourceId);
}

function editResource(resourceId) {
    alert('Edit resource: ' + resourceId);
}
</script>
{% endblock %}
