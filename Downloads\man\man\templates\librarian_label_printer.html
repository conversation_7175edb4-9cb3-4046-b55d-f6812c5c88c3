{% extends "base.html" %}

{% block title %}Label Printer - Librarian{% endblock %}

{% block sidebar %}
{% include 'librarian_sidebar.html' %}
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h2><i class="fas fa-print me-3 text-warning"></i>Label Printer</h2>
        <p class="text-muted mb-0">Generate and print labels for books and library resources</p>
    </div>
    <div>
        <button class="btn btn-success" onclick="printAllLabels()">
            <i class="fas fa-print me-2"></i>Print All Labels
        </button>
    </div>
</div>

<!-- Label Type Selection -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-tags me-2"></i>Label Type</h5>
            </div>
            <div class="card-body">
                <div class="form-check mb-3">
                    <input class="form-check-input" type="radio" name="labelType" id="bookLabels" value="book" checked>
                    <label class="form-check-label" for="bookLabels">
                        <strong>Book Labels</strong><br>
                        <small class="text-muted">Standard book spine labels with call numbers</small>
                    </label>
                </div>
                <div class="form-check mb-3">
                    <input class="form-check-input" type="radio" name="labelType" id="barcodeLabels" value="barcode">
                    <label class="form-check-label" for="barcodeLabels">
                        <strong>Barcode Labels</strong><br>
                        <small class="text-muted">Barcode labels for book identification</small>
                    </label>
                </div>
                <div class="form-check mb-3">
                    <input class="form-check-input" type="radio" name="labelType" id="shelfLabels" value="shelf">
                    <label class="form-check-label" for="shelfLabels">
                        <strong>Shelf Labels</strong><br>
                        <small class="text-muted">Labels for shelf organization</small>
                    </label>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-cog me-2"></i>Print Settings</h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <label for="labelSize" class="form-label">Label Size</label>
                    <select class="form-select" id="labelSize">
                        <option value="small">Small (1" x 2.625")</option>
                        <option value="medium" selected>Medium (1.33" x 4")</option>
                        <option value="large">Large (2" x 4")</option>
                    </select>
                </div>
                <div class="mb-3">
                    <label for="copies" class="form-label">Number of Copies</label>
                    <input type="number" class="form-control" id="copies" value="1" min="1" max="10">
                </div>
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="includeBorder" checked>
                    <label class="form-check-label" for="includeBorder">
                        Include border
                    </label>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Book Selection -->
<div class="card mb-4">
    <div class="card-header">
        <h5 class="mb-0"><i class="fas fa-search me-2"></i>Select Books</h5>
    </div>
    <div class="card-body">
        <div class="row mb-3">
            <div class="col-md-6">
                <label for="bookSearch" class="form-label">Search Books</label>
                <input type="text" class="form-control" id="bookSearch" placeholder="Search by title, author, or access number">
            </div>
            <div class="col-md-3">
                <label for="department" class="form-label">Department</label>
                <select class="form-select" id="department">
                    <option value="">All Departments</option>
                    <option value="CSE">Computer Science</option>
                    <option value="ECE">Electronics</option>
                    <option value="MECH">Mechanical</option>
                    <option value="CIVIL">Civil</option>
                </select>
            </div>
            <div class="col-md-3">
                <label for="category" class="form-label">Category</label>
                <select class="form-select" id="category">
                    <option value="">All Categories</option>
                    <option value="textbook">Textbook</option>
                    <option value="reference">Reference</option>
                    <option value="fiction">Fiction</option>
                </select>
            </div>
        </div>
        <button type="button" class="btn btn-primary" onclick="searchBooks()">
            <i class="fas fa-search me-2"></i>Search Books
        </button>
    </div>
</div>

<!-- Selected Books -->
<div class="card mb-4">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0"><i class="fas fa-list me-2"></i>Selected Books</h5>
        <span class="badge bg-primary" id="selectedCount">0 selected</span>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-striped table-hover">
                <thead class="table-dark">
                    <tr>
                        <th>
                            <input type="checkbox" id="selectAll" onchange="toggleSelectAll()">
                        </th>
                        <th>Access No.</th>
                        <th>Title</th>
                        <th>Author</th>
                        <th>Call Number</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody id="booksTableBody">
                    <tr>
                        <td colspan="6" class="text-center text-muted">Search for books to generate labels</td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Label Preview -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0"><i class="fas fa-eye me-2"></i>Label Preview</h5>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-6">
                <div class="label-preview border p-3 text-center" id="labelPreview">
                    <div class="label-content">
                        <div class="call-number fw-bold">004.1 SMI</div>
                        <div class="title">Introduction to Computer Science</div>
                        <div class="author">Smith, John</div>
                        <div class="barcode mt-2">||||| |||| |||||</div>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="d-flex flex-column h-100">
                    <button class="btn btn-outline-primary mb-2" onclick="previewLabels()">
                        <i class="fas fa-eye me-2"></i>Preview Selected
                    </button>
                    <button class="btn btn-success mb-2" onclick="printSelected()">
                        <i class="fas fa-print me-2"></i>Print Selected
                    </button>
                    <button class="btn btn-info" onclick="exportLabels()">
                        <i class="fas fa-download me-2"></i>Export PDF
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let selectedBooks = [];

$(document).ready(function() {
    updateLabelPreview();
    
    // Update preview when settings change
    $('input[name="labelType"], #labelSize, #includeBorder').on('change', updateLabelPreview);
});

function searchBooks() {
    const searchTerm = $('#bookSearch').val();
    const department = $('#department').val();
    const category = $('#category').val();
    
    // Show loading
    $('#booksTableBody').html('<tr><td colspan="6" class="text-center"><i class="fas fa-spinner fa-spin"></i> Searching...</td></tr>');
    
    // Simulate API call
    setTimeout(function() {
        const mockBooks = [
            {
                id: 'BK001',
                accessNo: 'CS001',
                title: 'Introduction to Computer Science',
                author: 'John Smith',
                callNumber: '004.1 SMI'
            },
            {
                id: 'BK002',
                accessNo: 'MATH001',
                title: 'Advanced Mathematics',
                author: 'Jane Doe',
                callNumber: '510 DOE'
            }
        ];
        
        populateBooksTable(mockBooks);
    }, 1000);
}

function populateBooksTable(books) {
    const tbody = $('#booksTableBody');
    tbody.empty();
    
    if (books.length === 0) {
        tbody.html('<tr><td colspan="6" class="text-center text-muted">No books found</td></tr>');
        return;
    }
    
    books.forEach(function(book) {
        const row = `
            <tr>
                <td>
                    <input type="checkbox" class="book-checkbox" value="${book.id}" data-book='${JSON.stringify(book)}'>
                </td>
                <td><strong>${book.accessNo}</strong></td>
                <td>${book.title}</td>
                <td>${book.author}</td>
                <td><code>${book.callNumber}</code></td>
                <td>
                    <button class="btn btn-sm btn-outline-primary" onclick="previewSingle('${book.id}')">
                        <i class="fas fa-eye"></i>
                    </button>
                </td>
            </tr>
        `;
        tbody.append(row);
    });
    
    // Update selected count when checkboxes change
    $('.book-checkbox').on('change', updateSelectedCount);
}

function toggleSelectAll() {
    const selectAll = document.getElementById('selectAll');
    const checkboxes = document.querySelectorAll('.book-checkbox');
    
    checkboxes.forEach(checkbox => {
        checkbox.checked = selectAll.checked;
    });
    
    updateSelectedCount();
}

function updateSelectedCount() {
    const selected = document.querySelectorAll('.book-checkbox:checked');
    $('#selectedCount').text(`${selected.length} selected`);
    
    selectedBooks = Array.from(selected).map(cb => JSON.parse(cb.dataset.book));
}

function updateLabelPreview() {
    const labelType = $('input[name="labelType"]:checked').val();
    const labelSize = $('#labelSize').val();
    const includeBorder = $('#includeBorder').is(':checked');
    
    let previewContent = '';
    
    switch(labelType) {
        case 'book':
            previewContent = `
                <div class="call-number fw-bold">004.1 SMI</div>
                <div class="title">Introduction to Computer Science</div>
                <div class="author">Smith, John</div>
            `;
            break;
        case 'barcode':
            previewContent = `
                <div class="barcode-number">123456789</div>
                <div class="barcode">||||| |||| |||||</div>
                <div class="title">Introduction to Computer Science</div>
            `;
            break;
        case 'shelf':
            previewContent = `
                <div class="shelf-range fw-bold">004.1 - 004.9</div>
                <div class="subject">Computer Science</div>
                <div class="department">CSE Department</div>
            `;
            break;
    }
    
    $('#labelPreview').html(`<div class="label-content">${previewContent}</div>`);
    
    // Update preview styling based on settings
    const preview = $('#labelPreview');
    preview.toggleClass('border', includeBorder);
    preview.removeClass('label-small label-medium label-large').addClass(`label-${labelSize}`);
}

function previewLabels() {
    if (selectedBooks.length === 0) {
        alert('Please select books to preview labels');
        return;
    }
    
    alert(`Preview would show ${selectedBooks.length} labels`);
}

function printSelected() {
    if (selectedBooks.length === 0) {
        alert('Please select books to print labels');
        return;
    }
    
    const copies = $('#copies').val();
    alert(`Printing ${selectedBooks.length} labels with ${copies} copies each`);
}

function printAllLabels() {
    alert('Print all labels functionality would be implemented here');
}

function exportLabels() {
    if (selectedBooks.length === 0) {
        alert('Please select books to export labels');
        return;
    }
    
    alert('Export PDF functionality would be implemented here');
}

function previewSingle(bookId) {
    alert('Preview single label for book: ' + bookId);
}
</script>

<style>
.label-preview {
    min-height: 150px;
    background: #f8f9fa;
    border-radius: 8px;
}

.label-content {
    font-family: 'Courier New', monospace;
    line-height: 1.2;
}

.label-small .label-content {
    font-size: 10px;
}

.label-medium .label-content {
    font-size: 12px;
}

.label-large .label-content {
    font-size: 14px;
}

.call-number {
    font-size: 1.2em;
    margin-bottom: 5px;
}

.barcode {
    font-family: 'Courier New', monospace;
    font-size: 1.5em;
    letter-spacing: 2px;
}
</style>
{% endblock %}
