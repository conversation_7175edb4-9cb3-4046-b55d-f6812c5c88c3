{% extends "base.html" %}

{% block title %}Transfer Report - Librarian{% endblock %}

{% block sidebar %}
{% include 'librarian_sidebar.html' %}
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h2><i class="fas fa-exchange-alt me-3 text-primary"></i>Transfer Report</h2>
        <p class="text-muted mb-0">Track book transfers between libraries and departments</p>
    </div>
    <div>
        <button class="btn btn-success" onclick="exportReport('excel')">
            <i class="fas fa-file-excel me-2"></i>Export Excel
        </button>
        <button class="btn btn-danger ms-2" onclick="exportReport('pdf')">
            <i class="fas fa-file-pdf me-2"></i>Export PDF
        </button>
    </div>
</div>

<!-- Filter Section -->
<div class="card mb-4">
    <div class="card-header">
        <h5 class="mb-0"><i class="fas fa-filter me-2"></i>Filters</h5>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-3">
                <label for="dateFrom" class="form-label">From Date</label>
                <input type="date" class="form-control" id="dateFrom">
            </div>
            <div class="col-md-3">
                <label for="dateTo" class="form-label">To Date</label>
                <input type="date" class="form-control" id="dateTo">
            </div>
            <div class="col-md-3">
                <label for="transferType" class="form-label">Transfer Type</label>
                <select class="form-select" id="transferType">
                    <option value="">All Types</option>
                    <option value="inter-library">Inter-Library</option>
                    <option value="department">Department</option>
                    <option value="branch">Branch</option>
                </select>
            </div>
            <div class="col-md-3">
                <label for="status" class="form-label">Status</label>
                <select class="form-select" id="status">
                    <option value="">All Status</option>
                    <option value="pending">Pending</option>
                    <option value="in-transit">In Transit</option>
                    <option value="completed">Completed</option>
                    <option value="cancelled">Cancelled</option>
                </select>
            </div>
        </div>
        <div class="row mt-3">
            <div class="col-12">
                <button class="btn btn-primary" onclick="applyFilters()">
                    <i class="fas fa-search me-2"></i>Apply Filters
                </button>
                <button class="btn btn-secondary ms-2" onclick="resetFilters()">
                    <i class="fas fa-undo me-2"></i>Reset
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">Total Transfers</h6>
                        <h3 class="mb-0" id="totalTransfers">0</h3>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-exchange-alt fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">Completed</h6>
                        <h3 class="mb-0" id="completedTransfers">0</h3>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-check-circle fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">In Transit</h6>
                        <h3 class="mb-0" id="inTransitTransfers">0</h3>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-truck fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">Avg Time</h6>
                        <h3 class="mb-0" id="avgTransferTime">0 days</h3>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-clock fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Transfer Records Table -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0"><i class="fas fa-table me-2"></i>Transfer Records</h5>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-striped table-hover" id="transferTable">
                <thead class="table-dark">
                    <tr>
                        <th>Transfer ID</th>
                        <th>Book Title</th>
                        <th>Access No.</th>
                        <th>From</th>
                        <th>To</th>
                        <th>Transfer Date</th>
                        <th>Expected Date</th>
                        <th>Status</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody id="transferTableBody">
                    <!-- Data will be loaded here -->
                </tbody>
            </table>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// Initialize the page
$(document).ready(function() {
    loadTransferData();
    
    // Set default dates (last 30 days)
    const today = new Date();
    const lastMonth = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000);
    
    $('#dateTo').val(today.toISOString().split('T')[0]);
    $('#dateFrom').val(lastMonth.toISOString().split('T')[0]);
});

function loadTransferData() {
    // Show loading state
    $('#transferTableBody').html('<tr><td colspan="9" class="text-center"><i class="fas fa-spinner fa-spin"></i> Loading...</td></tr>');
    
    // Simulate API call with mock data
    setTimeout(function() {
        const mockData = {
            statistics: {
                totalTransfers: 45,
                completedTransfers: 38,
                inTransitTransfers: 5,
                avgTransferTime: 3
            },
            data: [
                {
                    id: '1',
                    transferId: 'TRF001',
                    bookTitle: 'Introduction to Computer Science',
                    accessNo: 'CS001',
                    from: 'Main Library',
                    to: 'CS Department',
                    transferDate: '2024-01-10',
                    expectedDate: '2024-01-15',
                    status: 'completed'
                },
                {
                    id: '2',
                    transferId: 'TRF002',
                    bookTitle: 'Advanced Mathematics',
                    accessNo: 'MATH001',
                    from: 'Main Library',
                    to: 'Branch Library',
                    transferDate: '2024-01-12',
                    expectedDate: '2024-01-17',
                    status: 'in-transit'
                }
            ]
        };
        
        updateStatistics(mockData.statistics);
        populateTable(mockData.data);
    }, 1000);
}

function updateStatistics(stats) {
    $('#totalTransfers').text(stats.totalTransfers || 0);
    $('#completedTransfers').text(stats.completedTransfers || 0);
    $('#inTransitTransfers').text(stats.inTransitTransfers || 0);
    $('#avgTransferTime').text((stats.avgTransferTime || 0) + ' days');
}

function populateTable(data) {
    const tbody = $('#transferTableBody');
    tbody.empty();
    
    if (data.length === 0) {
        tbody.html('<tr><td colspan="9" class="text-center">No transfer records found</td></tr>');
        return;
    }
    
    data.forEach(function(transfer) {
        const row = `
            <tr>
                <td><strong>${transfer.transferId}</strong></td>
                <td>${transfer.bookTitle}</td>
                <td>${transfer.accessNo}</td>
                <td>${transfer.from}</td>
                <td>${transfer.to}</td>
                <td>${transfer.transferDate}</td>
                <td>${transfer.expectedDate}</td>
                <td><span class="badge bg-${getStatusColor(transfer.status)}">${transfer.status}</span></td>
                <td>
                    <button class="btn btn-sm btn-outline-primary" onclick="viewTransfer('${transfer.id}')">
                        <i class="fas fa-eye"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-warning" onclick="trackTransfer('${transfer.id}')">
                        <i class="fas fa-map-marker-alt"></i>
                    </button>
                </td>
            </tr>
        `;
        tbody.append(row);
    });
}

function getStatusColor(status) {
    const colors = {
        'pending': 'secondary',
        'in-transit': 'warning',
        'completed': 'success',
        'cancelled': 'danger'
    };
    return colors[status] || 'secondary';
}

function applyFilters() {
    loadTransferData();
}

function resetFilters() {
    $('#dateFrom').val('');
    $('#dateTo').val('');
    $('#transferType').val('');
    $('#status').val('');
    loadTransferData();
}

function exportReport(format) {
    alert('Export functionality would be implemented here for format: ' + format);
}

function viewTransfer(transferId) {
    alert('View transfer details: ' + transferId);
}

function trackTransfer(transferId) {
    alert('Track transfer: ' + transferId);
}
</script>
{% endblock %}
