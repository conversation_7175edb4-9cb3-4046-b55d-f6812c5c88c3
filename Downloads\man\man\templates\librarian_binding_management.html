{% extends "base.html" %}

{% block title %}Binding & Maintenance - Librarian{% endblock %}

{% block sidebar %}
{% include 'librarian_sidebar.html' %}
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2><i class="fas fa-tools me-3"></i>Binding & Maintenance Management</h2>
    <div>
        <button class="btn btn-outline-primary btn-custom" onclick="showBindingStatus()">
            <i class="fas fa-list me-2"></i>View All Status
        </button>
    </div>
</div>

<!-- Binding Management Tabs -->
<ul class="nav nav-tabs mb-4" id="bindingTabs" role="tablist">
    <li class="nav-item" role="presentation">
        <button class="nav-link active" id="send-binding-tab" data-bs-toggle="tab" data-bs-target="#send-binding" type="button" role="tab">
            <i class="fas fa-arrow-up me-2"></i>Send to Binding
        </button>
    </li>
    <li class="nav-item" role="presentation">
        <button class="nav-link" id="binding-status-tab" data-bs-toggle="tab" data-bs-target="#binding-status" type="button" role="tab">
            <i class="fas fa-list me-2"></i>Binding Status
        </button>
    </li>
    <li class="nav-item" role="presentation">
        <button class="nav-link" id="receive-binding-tab" data-bs-toggle="tab" data-bs-target="#receive-binding" type="button" role="tab">
            <i class="fas fa-arrow-down me-2"></i>Receive from Binding
        </button>
    </li>
</ul>

<div class="tab-content" id="bindingTabContent">
    <!-- Send to Binding Tab -->
    <div class="tab-pane fade show active" id="send-binding" role="tabpanel">
        <div class="row">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-arrow-up me-2"></i>Send Book to Binding/Repair</h5>
                    </div>
                    <div class="card-body">
                        <form id="sendBindingForm">
                            <!-- Book Search -->
                            <div class="row mb-3">
                                <div class="col-md-12">
                                    <label for="bindingBookSearch" class="form-label">Book Access Number/Title <span class="text-danger">*</span></label>
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="fas fa-book"></i></span>
                                        <input type="text" class="form-control" id="bindingBookSearch" placeholder="Scan or type book access number" required>
                                        <button type="button" class="btn btn-outline-secondary" onclick="searchBindingBook()">
                                            <i class="fas fa-search"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                            
                            <div id="bindingBookInfo" class="alert alert-info" style="display: none;">
                                <h6><i class="fas fa-book me-2"></i>Book Information</h6>
                                <div id="bindingBookDetails"></div>
                            </div>
                            
                            <!-- Vendor Selection -->
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label for="vendorSelect" class="form-label">Vendor <span class="text-danger">*</span></label>
                                    <select class="form-select" id="vendorSelect" required>
                                        <option value="">Select Vendor</option>
                                        <option value="ABC Binding Works">ABC Binding Works</option>
                                        <option value="XYZ Book Repair">XYZ Book Repair</option>
                                        <option value="Quick Bind Services">Quick Bind Services</option>
                                    </select>
                                </div>
                                <div class="col-md-6">
                                    <label for="serviceType" class="form-label">Service Type <span class="text-danger">*</span></label>
                                    <select class="form-select" id="serviceType" required>
                                        <option value="">Select Service</option>
                                        <option value="Binding">Binding</option>
                                        <option value="Repair">Repair</option>
                                        <option value="Rebinding">Rebinding</option>
                                        <option value="Cover Replacement">Cover Replacement</option>
                                    </select>
                                </div>
                            </div>
                            
                            <!-- Dates and Cost -->
                            <div class="row mb-3">
                                <div class="col-md-4">
                                    <label for="dateSent" class="form-label">Date Sent</label>
                                    <input type="date" class="form-control" id="dateSent" readonly>
                                </div>
                                <div class="col-md-4">
                                    <label for="expectedReturn" class="form-label">Expected Return Date</label>
                                    <input type="date" class="form-control" id="expectedReturn">
                                </div>
                                <div class="col-md-4">
                                    <label for="estimatedCost" class="form-label">Estimated Cost (₹)</label>
                                    <input type="number" class="form-control" id="estimatedCost" step="0.01" min="0">
                                </div>
                            </div>
                            
                            <!-- Notes -->
                            <div class="mb-3">
                                <label for="bindingNotes" class="form-label">Notes/Special Instructions</label>
                                <textarea class="form-control" id="bindingNotes" rows="3" placeholder="Describe the issue and any special instructions"></textarea>
                            </div>
                            
                            <div class="text-center">
                                <button type="submit" class="btn btn-warning btn-lg">
                                    <i class="fas fa-tools me-2"></i>Send to Binding
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-info-circle me-2"></i>Binding Statistics</h6>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <small class="text-muted">Books Currently in Binding</small>
                            <div class="fw-bold text-warning">5</div>
                        </div>
                        <div class="mb-3">
                            <small class="text-muted">Average Turnaround Time</small>
                            <div class="fw-bold">7-10 Days</div>
                        </div>
                        <div class="mb-3">
                            <small class="text-muted">Total Cost (This Month)</small>
                            <div class="fw-bold text-primary">₹2,500</div>
                        </div>
                        <div class="mb-3">
                            <small class="text-muted">Overdue Returns</small>
                            <div class="fw-bold text-danger">2</div>
                        </div>
                    </div>
                </div>
                
                <div class="card mt-3">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-exclamation-triangle me-2"></i>Important Notes</h6>
                    </div>
                    <div class="card-body">
                        <ul class="small mb-0">
                            <li>Books sent for binding will be marked as "Not Available"</li>
                            <li>Ensure book is not currently issued</li>
                            <li>Take photos of damage before sending</li>
                            <li>Keep vendor receipt for tracking</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Binding Status Tab -->
    <div class="tab-pane fade" id="binding-status" role="tabpanel">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-list me-2"></i>Books in Binding</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>Access No.</th>
                                <th>Title</th>
                                <th>Vendor</th>
                                <th>Service Type</th>
                                <th>Date Sent</th>
                                <th>Expected Return</th>
                                <th>Status</th>
                                <th>Cost</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>B001</td>
                                <td>Sample Book Title</td>
                                <td>ABC Binding Works</td>
                                <td>Repair</td>
                                <td>2024-01-10</td>
                                <td>2024-01-20</td>
                                <td><span class="badge bg-warning">In Progress</span></td>
                                <td>₹150</td>
                                <td>
                                    <button class="btn btn-sm btn-outline-success" onclick="markAsReceived('B001')">
                                        <i class="fas fa-check"></i> Received
                                    </button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Receive from Binding Tab -->
    <div class="tab-pane fade" id="receive-binding" role="tabpanel">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-arrow-down me-2"></i>Receive Book from Binding</h5>
            </div>
            <div class="card-body">
                <form id="receiveBindingForm">
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="receiveBookSearch" class="form-label">Book Access Number <span class="text-danger">*</span></label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-book"></i></span>
                                <input type="text" class="form-control" id="receiveBookSearch" placeholder="Scan book access number" required>
                                <button type="button" class="btn btn-outline-secondary" onclick="searchReceiveBook()">
                                    <i class="fas fa-search"></i>
                                </button>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <label for="actualReturnDate" class="form-label">Actual Return Date</label>
                            <input type="date" class="form-control" id="actualReturnDate">
                        </div>
                    </div>
                    
                    <div id="receiveBookInfo" class="alert alert-success" style="display: none;">
                        <h6><i class="fas fa-info-circle me-2"></i>Binding Information</h6>
                        <div id="receiveBookDetails"></div>
                    </div>
                    
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="actualCost" class="form-label">Actual Cost (₹)</label>
                            <input type="number" class="form-control" id="actualCost" step="0.01" min="0">
                        </div>
                        <div class="col-md-6">
                            <label for="qualityRating" class="form-label">Quality Rating</label>
                            <select class="form-select" id="qualityRating">
                                <option value="">Rate the work quality</option>
                                <option value="5">Excellent (5/5)</option>
                                <option value="4">Good (4/5)</option>
                                <option value="3">Average (3/5)</option>
                                <option value="2">Poor (2/5)</option>
                                <option value="1">Very Poor (1/5)</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="receiveNotes" class="form-label">Completion Notes</label>
                        <textarea class="form-control" id="receiveNotes" rows="3" placeholder="Notes about the completed work"></textarea>
                    </div>
                    
                    <div class="text-center">
                        <button type="submit" class="btn btn-success btn-lg">
                            <i class="fas fa-check me-2"></i>Mark as Received & Make Available
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Set current date for date sent
    $('#dateSent').val(new Date().toISOString().split('T')[0]);
    $('#actualReturnDate').val(new Date().toISOString().split('T')[0]);
});

function searchBindingBook() {
    const query = $('#bindingBookSearch').val();
    if (query) {
        $('#bindingBookInfo').show();
        $('#bindingBookDetails').html(`
            <strong>Sample Book Title</strong><br>
            Access No: ${query}<br>
            Author: Sample Author<br>
            Status: Available for Binding<br>
            Current Condition: Good
        `);
    }
}

function searchReceiveBook() {
    const query = $('#receiveBookSearch').val();
    if (query) {
        $('#receiveBookInfo').show();
        $('#receiveBookDetails').html(`
            <strong>Binding Record Found</strong><br>
            Book: Sample Book Title<br>
            Vendor: ABC Binding Works<br>
            Service: Repair<br>
            Date Sent: 2024-01-10<br>
            Expected Return: 2024-01-20<br>
            Estimated Cost: ₹150
        `);
        $('#actualCost').val('150');
    }
}

$('#sendBindingForm').on('submit', function(e) {
    e.preventDefault();
    alert('Book sent to binding successfully! Book status updated to "In Binding".');
    $('#sendBindingForm')[0].reset();
    $('#bindingBookInfo').hide();
    $('#dateSent').val(new Date().toISOString().split('T')[0]);
});

$('#receiveBindingForm').on('submit', function(e) {
    e.preventDefault();
    alert('Book received from binding successfully! Book is now available for circulation.');
    $('#receiveBindingForm')[0].reset();
    $('#receiveBookInfo').hide();
    $('#actualReturnDate').val(new Date().toISOString().split('T')[0]);
});

function markAsReceived(accessNo) {
    if (confirm('Mark this book as received from binding?')) {
        alert('Book marked as received and made available for circulation.');
    }
}

function showBindingStatus() {
    $('#binding-status-tab').click();
}
</script>
{% endblock %}
