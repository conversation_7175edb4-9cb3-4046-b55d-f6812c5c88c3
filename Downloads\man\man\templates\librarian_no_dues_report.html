{% extends "base.html" %}

{% block title %}No Dues Report - Librarian{% endblock %}

{% block sidebar %}
{% include 'librarian_sidebar.html' %}
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h2><i class="fas fa-check-circle me-3 text-success"></i>No Dues Report</h2>
        <p class="text-muted mb-0">Generate and manage no dues certificates for students</p>
    </div>
    <div>
        <button class="btn btn-success" onclick="exportReport('excel')">
            <i class="fas fa-file-excel me-2"></i>Export Excel
        </button>
        <button class="btn btn-danger ms-2" onclick="exportReport('pdf')">
            <i class="fas fa-file-pdf me-2"></i>Export PDF
        </button>
    </div>
</div>

<!-- Search Section -->
<div class="card mb-4">
    <div class="card-header">
        <h5 class="mb-0"><i class="fas fa-search me-2"></i>Search Student</h5>
    </div>
    <div class="card-body">
        <form id="searchForm" class="row g-3">
            <div class="col-md-4">
                <label for="studentId" class="form-label">Student ID</label>
                <input type="text" class="form-control" id="studentId" name="studentId" placeholder="Enter Student ID">
            </div>
            <div class="col-md-4">
                <label for="studentName" class="form-label">Student Name</label>
                <input type="text" class="form-control" id="studentName" name="studentName" placeholder="Enter Student Name">
            </div>
            <div class="col-md-4">
                <label for="department" class="form-label">Department</label>
                <select class="form-select" id="department" name="department">
                    <option value="">All Departments</option>
                    <option value="CSE">Computer Science</option>
                    <option value="ECE">Electronics</option>
                    <option value="MECH">Mechanical</option>
                    <option value="CIVIL">Civil</option>
                </select>
            </div>
            <div class="col-12">
                <button type="button" class="btn btn-primary" onclick="searchStudent()">
                    <i class="fas fa-search me-2"></i>Search
                </button>
                <button type="button" class="btn btn-secondary ms-2" onclick="resetSearch()">
                    <i class="fas fa-undo me-2"></i>Reset
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">Eligible Students</h6>
                        <h3 class="mb-0" id="eligibleStudents">0</h3>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-check-circle fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">Pending Returns</h6>
                        <h3 class="mb-0" id="pendingReturns">0</h3>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-clock fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-danger text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">Outstanding Fines</h6>
                        <h3 class="mb-0" id="outstandingFines">0</h3>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-exclamation-triangle fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">Certificates Issued</h6>
                        <h3 class="mb-0" id="certificatesIssued">0</h3>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-certificate fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Students Table -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0"><i class="fas fa-table me-2"></i>Student No Dues Status</h5>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-striped table-hover" id="studentTable">
                <thead class="table-dark">
                    <tr>
                        <th>Student ID</th>
                        <th>Name</th>
                        <th>Department</th>
                        <th>Books Issued</th>
                        <th>Overdue Books</th>
                        <th>Outstanding Fine</th>
                        <th>Status</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody id="studentTableBody">
                    <!-- Data will be loaded here -->
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- No Dues Certificate Modal -->
<div class="modal fade" id="noDuesModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">No Dues Certificate</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="certificateContent" class="text-center p-4">
                    <h3>LIBRARY NO DUES CERTIFICATE</h3>
                    <hr>
                    <p>This is to certify that <strong id="certStudentName"></strong> (ID: <strong id="certStudentId"></strong>) from <strong id="certDepartment"></strong> department has no outstanding dues with the library.</p>
                    <p>All borrowed books have been returned and no fines are pending.</p>
                    <br>
                    <p>Date: <span id="certDate"></span></p>
                    <br>
                    <p>Librarian Signature: ___________________</p>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary" onclick="printCertificate()">
                    <i class="fas fa-print me-2"></i>Print
                </button>
                <button type="button" class="btn btn-success" onclick="downloadCertificate()">
                    <i class="fas fa-download me-2"></i>Download PDF
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    loadStudentData();
});

function loadStudentData() {
    // Show loading state
    $('#studentTableBody').html('<tr><td colspan="8" class="text-center"><i class="fas fa-spinner fa-spin"></i> Loading...</td></tr>');
    
    // Simulate API call with mock data
    setTimeout(function() {
        const mockData = {
            statistics: {
                eligibleStudents: 1180,
                pendingReturns: 45,
                outstandingFines: 12,
                certificatesIssued: 234
            },
            data: [
                {
                    id: 'STU001',
                    name: 'John Doe',
                    department: 'CSE',
                    booksIssued: 0,
                    overdueBooks: 0,
                    outstandingFine: 0,
                    status: 'Eligible'
                },
                {
                    id: 'STU002',
                    name: 'Jane Smith',
                    department: 'ECE',
                    booksIssued: 2,
                    overdueBooks: 1,
                    outstandingFine: 50,
                    status: 'Not Eligible'
                }
            ]
        };
        
        updateStatistics(mockData.statistics);
        populateTable(mockData.data);
    }, 1000);
}

function updateStatistics(stats) {
    $('#eligibleStudents').text(stats.eligibleStudents || 0);
    $('#pendingReturns').text(stats.pendingReturns || 0);
    $('#outstandingFines').text(stats.outstandingFines || 0);
    $('#certificatesIssued').text(stats.certificatesIssued || 0);
}

function populateTable(data) {
    const tbody = $('#studentTableBody');
    tbody.empty();
    
    if (data.length === 0) {
        tbody.html('<tr><td colspan="8" class="text-center">No students found</td></tr>');
        return;
    }
    
    data.forEach(function(student) {
        const row = `
            <tr>
                <td><strong>${student.id}</strong></td>
                <td>${student.name}</td>
                <td>${student.department}</td>
                <td>${student.booksIssued}</td>
                <td>${student.overdueBooks}</td>
                <td>₹${student.outstandingFine}</td>
                <td><span class="badge bg-${getStatusColor(student.status)}">${student.status}</span></td>
                <td>
                    ${student.status === 'Eligible' ? 
                        `<button class="btn btn-sm btn-success" onclick="generateCertificate('${student.id}', '${student.name}', '${student.department}')">
                            <i class="fas fa-certificate"></i> Generate
                        </button>` :
                        `<button class="btn btn-sm btn-outline-primary" onclick="viewDetails('${student.id}')">
                            <i class="fas fa-eye"></i> Details
                        </button>`
                    }
                </td>
            </tr>
        `;
        tbody.append(row);
    });
}

function getStatusColor(status) {
    const colors = {
        'Eligible': 'success',
        'Not Eligible': 'danger',
        'Pending': 'warning'
    };
    return colors[status] || 'secondary';
}

function searchStudent() {
    loadStudentData();
}

function resetSearch() {
    $('#searchForm')[0].reset();
    loadStudentData();
}

function generateCertificate(studentId, studentName, department) {
    $('#certStudentId').text(studentId);
    $('#certStudentName').text(studentName);
    $('#certDepartment').text(department);
    $('#certDate').text(new Date().toLocaleDateString());
    $('#noDuesModal').modal('show');
}

function printCertificate() {
    const printContent = document.getElementById('certificateContent').innerHTML;
    const printWindow = window.open('', '', 'height=600,width=800');
    printWindow.document.write('<html><head><title>No Dues Certificate</title>');
    printWindow.document.write('<style>body{font-family:Arial,sans-serif;padding:20px;}</style>');
    printWindow.document.write('</head><body>');
    printWindow.document.write(printContent);
    printWindow.document.write('</body></html>');
    printWindow.document.close();
    printWindow.print();
}

function downloadCertificate() {
    alert('PDF download functionality would be implemented here');
}

function viewDetails(studentId) {
    alert('View student details: ' + studentId);
}

function exportReport(format) {
    alert('Export functionality would be implemented here for format: ' + format);
}
</script>
{% endblock %}
