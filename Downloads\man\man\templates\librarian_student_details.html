{% extends "base.html" %}

{% block title %}Student Details - Librarian{% endblock %}

{% block sidebar %}
<a class="nav-link" href="{{ url_for('librarian_dashboard') }}">
    <i class="fas fa-tachometer-alt me-2"></i>Dashboard
</a>
<a class="nav-link active" href="{{ url_for('librarian_students') }}">
    <i class="fas fa-graduation-cap me-2"></i>Manage Students
</a>
<a class="nav-link" href="{{ url_for('librarian_books') }}">
    <i class="fas fa-book me-2"></i>Manage Books
</a>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2><i class="fas fa-user me-3"></i>Student Details</h2>
    <div>
        <a href="{{ url_for('librarian_edit_student', student_id=student.id) }}" class="btn btn-warning me-2">
            <i class="fas fa-edit me-2"></i>Edit Student
        </a>
        <a href="{{ url_for('librarian_students') }}" class="btn btn-secondary btn-custom">
            <i class="fas fa-arrow-left me-2"></i>Back to Students
        </a>
    </div>
</div>

<div class="card mb-4">
    <div class="card-header bg-info text-white">
        <h5 class="mb-0"><i class="fas fa-id-card me-2"></i>{{ student.name }}</h5>
    </div>
    <div class="card-body">
        <dl class="row mb-0">
            <dt class="col-sm-3">User ID</dt>
            <dd class="col-sm-9">{{ student.user_id }}</dd>
            <dt class="col-sm-3">Email</dt>
            <dd class="col-sm-9">{{ student.email }}</dd>
            <dt class="col-sm-3">Department</dt>
            <dd class="col-sm-9">{{ student.department }}</dd>
            <dt class="col-sm-3">Course</dt>
            <dd class="col-sm-9">{{ student.course }}</dd>
            <dt class="col-sm-3">Current Year</dt>
            <dd class="col-sm-9">{{ student.current_year }}</dd>
            <dt class="col-sm-3">Validity</dt>
            <dd class="col-sm-9">{{ student.validity_date.strftime('%Y-%m-%d') if student.validity_date else '' }}</dd>
        </dl>
    </div>
</div>

<div class="card">
    <div class="card-header bg-primary text-white">
        <h5 class="mb-0"><i class="fas fa-book-reader me-2"></i>Currently Borrowed Books</h5>
    </div>
    <div class="card-body p-0">
        {% if borrowed_books %}
        <div class="table-responsive">
            <table class="table table-striped table-hover mb-0">
                <thead class="table-light">
                    <tr>
                        <th>Access No</th>
                        <th>Title</th>
                        <th>Issue Date</th>
                        <th>Due Date</th>
                    </tr>
                </thead>
                <tbody>
                    {% for issue in borrowed_books %}
                    <tr>
                        <td>{{ issue.book.access_no }}</td>
                        <td>{{ issue.book.title }}</td>
                        <td>{{ issue.issue_date.strftime('%Y-%m-%d') }}</td>
                        <td>{{ issue.due_date.strftime('%Y-%m-%d') }}</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="alert alert-info m-3">
            <i class="fas fa-info-circle me-2"></i>No books currently borrowed.
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}
