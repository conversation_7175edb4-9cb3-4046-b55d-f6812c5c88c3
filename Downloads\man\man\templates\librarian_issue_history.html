{% extends "base.html" %}

{% block title %}Issue History - Librarian{% endblock %}

{% block sidebar %}
{% include 'librarian_sidebar.html' %}
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2><i class="fas fa-history me-3"></i>Issue History</h2>
    <a href="{{ url_for('librarian_issue_return') }}" class="btn btn-primary btn-custom">
        <i class="fas fa-exchange-alt me-2"></i>Issue/Return Books
    </a>
</div>

<!-- Search and Filter Section -->
<div class="row mb-4">
    <div class="col-md-4">
        <div class="input-group">
            <input type="text" class="form-control" id="searchHistory" placeholder="Search by student ID, name, or book...">
            <button class="btn btn-outline-secondary" type="button" id="searchBtn">
                <i class="fas fa-search"></i>
            </button>
        </div>
    </div>
    <div class="col-md-3">
        <select class="form-select" id="filterStatus">
            <option value="">All Status</option>
            <option value="issued">Currently Issued</option>
            <option value="returned">Returned</option>
            <option value="overdue">Overdue</option>
        </select>
    </div>
    <div class="col-md-3">
        <input type="date" class="form-control" id="filterDate" placeholder="Filter by date">
    </div>
    <div class="col-md-2">
        <button class="btn btn-outline-danger w-100" onclick="clearFilters()">
            <i class="fas fa-times me-2"></i>Clear
        </button>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card text-center border-primary">
            <div class="card-body">
                <i class="fas fa-list fa-2x text-primary mb-2"></i>
                <h4 class="text-primary">{{ issues|length }}</h4>
                <p class="mb-0">Total Issues</p>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card text-center border-warning">
            <div class="card-body">
                <i class="fas fa-hand-holding fa-2x text-warning mb-2"></i>
                <h4 class="text-warning">{{ issues|selectattr('return_date', 'none')|list|length }}</h4>
                <p class="mb-0">Currently Issued</p>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card text-center border-success">
            <div class="card-body">
                <i class="fas fa-check-circle fa-2x text-success mb-2"></i>
                <h4 class="text-success">{{ issues|selectattr('return_date', 'defined')|list|length }}</h4>
                <p class="mb-0">Returned</p>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card text-center border-danger">
            <div class="card-body">
                <i class="fas fa-exclamation-triangle fa-2x text-danger mb-2"></i>
                <h4 class="text-danger">
                    {{ overdue_count }}
                </h4>
                <p class="mb-0">Overdue</p>
            </div>
        </div>
    </div>
</div>

<!-- Issue History Table -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0"><i class="fas fa-list me-2"></i>Issue Records</h5>
    </div>
    <div class="card-body">
        {% if issues %}
        <div class="table-responsive">
            <table class="table table-striped table-hover" id="historyTable">
                <thead class="table-dark">
                    <tr>
                        <th>Issue ID</th>
                        <th>Student ID</th>
                        <th>Student Name</th>
                        <th>Book Title</th>
                        <th>Access No.</th>
                        <th>Issue Date</th>
                        <th>Due Date</th>
                        <th>Return Date</th>
                        <th>Status</th>
                        <th>Days</th>
                    </tr>
                </thead>
                <tbody>
                    {% for issue in issues %}
                    <tr>
                        <td><strong>#{{ issue.id }}</strong></td>
                        <td>{{ issue.student.user_id }}</td>
                        <td>{{ issue.student.username }}</td>
                        <td>{{ issue.book.title }}</td>
                        <td><span class="badge bg-info">{{ issue.book.access_no }}</span></td>
                        <td>{{ issue.issue_date.strftime('%d-%m-%Y') }}</td>
                        <td>{{ issue.due_date.strftime('%d-%m-%Y') }}</td>
                        <td>
                            {% if issue.return_date %}
                                {{ issue.return_date.strftime('%d-%m-%Y') }}
                            {% else %}
                                <span class="text-muted">Not returned</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if issue.return_date %}
                                <span class="badge bg-success">Returned</span>
                            {% elif issue.due_date < now %}
                                <span class="badge bg-danger">Overdue</span>
                            {% else %}
                                <span class="badge bg-warning">Issued</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if issue.return_date %}
                                {% set days = (issue.return_date - issue.issue_date).days %}
                                <span class="text-success">{{ days }} days</span>
                            {% else %}
                                {% set days = (now - issue.issue_date).days %}
                                {% if days > 14 %}
                                    <span class="text-danger">{{ days }} days</span>
                                {% else %}
                                    <span class="text-info">{{ days }} days</span>
                                {% endif %}
                            {% endif %}
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="text-center py-5">
            <i class="fas fa-history fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">No issue history found</h5>
            <p class="text-muted">Book issues will appear here once you start issuing books.</p>
            <a href="{{ url_for('librarian_issue_return') }}" class="btn btn-primary">
                <i class="fas fa-hand-holding me-2"></i>Issue First Book
            </a>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Search and filter functionality
document.getElementById('searchHistory').addEventListener('input', function() {
    filterTable();
});

document.getElementById('filterStatus').addEventListener('change', function() {
    filterTable();
});

document.getElementById('filterDate').addEventListener('change', function() {
    filterTable();
});

function filterTable() {
    const searchTerm = document.getElementById('searchHistory').value.toLowerCase();
    const statusFilter = document.getElementById('filterStatus').value;
    const dateFilter = document.getElementById('filterDate').value;
    const table = document.getElementById('historyTable');
    
    if (!table) return;
    
    const rows = table.getElementsByTagName('tbody')[0].getElementsByTagName('tr');

    for (let i = 0; i < rows.length; i++) {
        const row = rows[i];
        const cells = row.getElementsByTagName('td');
        let showRow = true;

        // Search filter
        if (searchTerm) {
            const studentId = cells[1].textContent.toLowerCase();
            const studentName = cells[2].textContent.toLowerCase();
            const bookTitle = cells[3].textContent.toLowerCase();
            const accessNo = cells[4].textContent.toLowerCase();
            
            if (!studentId.includes(searchTerm) && 
                !studentName.includes(searchTerm) && 
                !bookTitle.includes(searchTerm) &&
                !accessNo.includes(searchTerm)) {
                showRow = false;
            }
        }

        // Status filter
        if (statusFilter && showRow) {
            const statusBadge = cells[8].querySelector('.badge');
            if (statusBadge) {
                const status = statusBadge.textContent.toLowerCase();
                if (statusFilter === 'issued' && status !== 'issued') {
                    showRow = false;
                } else if (statusFilter === 'returned' && status !== 'returned') {
                    showRow = false;
                } else if (statusFilter === 'overdue' && status !== 'overdue') {
                    showRow = false;
                }
            }
        }

        // Date filter
        if (dateFilter && showRow) {
            const issueDate = cells[5].textContent;
            const issueDateFormatted = issueDate.split('-').reverse().join('-'); // Convert DD-MM-YYYY to YYYY-MM-DD
            if (issueDateFormatted !== dateFilter) {
                showRow = false;
            }
        }

        row.style.display = showRow ? '' : 'none';
    }
}

function clearFilters() {
    document.getElementById('searchHistory').value = '';
    document.getElementById('filterStatus').value = '';
    document.getElementById('filterDate').value = '';
    filterTable();
}

// Add current date as default max for date filter
document.getElementById('filterDate').max = new Date().toISOString().split('T')[0];
</script>
{% endblock %}
