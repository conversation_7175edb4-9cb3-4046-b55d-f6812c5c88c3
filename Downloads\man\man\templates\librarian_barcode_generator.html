{% extends "base.html" %}

{% block title %}Barcode Generator - Librarian{% endblock %}

{% block sidebar %}
{% include 'librarian_sidebar.html' %}
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h2><i class="fas fa-barcode me-3 text-primary"></i>Barcode Generator</h2>
        <p class="text-muted mb-0">Generate barcodes for books, students, and library items</p>
    </div>
</div>

<!-- Barcode Type Selection -->
<div class="row mb-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-cog me-2"></i>Barcode Type</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="barcodeType" id="bookBarcode" value="book" checked>
                            <label class="form-check-label" for="bookBarcode">
                                <i class="fas fa-book me-2"></i>Book Barcode
                            </label>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="barcodeType" id="studentBarcode" value="student">
                            <label class="form-check-label" for="studentBarcode">
                                <i class="fas fa-graduation-cap me-2"></i>Student ID Barcode
                            </label>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="barcodeType" id="customBarcode" value="custom">
                            <label class="form-check-label" for="customBarcode">
                                <i class="fas fa-edit me-2"></i>Custom Barcode
                            </label>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Book Barcode Section -->
<div id="bookBarcodeSection" class="barcode-section">
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="mb-0"><i class="fas fa-book me-2"></i>Book Barcode Generator</h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <label for="bookSearch" class="form-label">Search Book</label>
                    <input type="text" class="form-control" id="bookSearch" placeholder="Search by title, author, or access number">
                    <div id="bookSearchResults" class="mt-2"></div>
                </div>
                <div class="col-md-6">
                    <label for="selectedBook" class="form-label">Selected Book</label>
                    <div id="selectedBookInfo" class="border rounded p-3 bg-light">
                        <p class="text-muted mb-0">No book selected</p>
                    </div>
                </div>
            </div>
            <div class="row mt-3">
                <div class="col-md-6">
                    <label for="barcodeFormat" class="form-label">Barcode Format</label>
                    <select class="form-select" id="barcodeFormat">
                        <option value="CODE128">CODE128</option>
                        <option value="CODE39">CODE39</option>
                        <option value="EAN13">EAN13</option>
                        <option value="UPC">UPC</option>
                    </select>
                </div>
                <div class="col-md-6">
                    <label for="barcodeSize" class="form-label">Size</label>
                    <select class="form-select" id="barcodeSize">
                        <option value="small">Small (1x0.5 inch)</option>
                        <option value="medium" selected>Medium (2x1 inch)</option>
                        <option value="large">Large (3x1.5 inch)</option>
                    </select>
                </div>
            </div>
            <div class="row mt-3">
                <div class="col-12">
                    <button class="btn btn-primary" onclick="generateBookBarcode()">
                        <i class="fas fa-barcode me-2"></i>Generate Barcode
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Student Barcode Section -->
<div id="studentBarcodeSection" class="barcode-section" style="display: none;">
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="mb-0"><i class="fas fa-graduation-cap me-2"></i>Student ID Barcode Generator</h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <label for="studentSearch" class="form-label">Search Student</label>
                    <input type="text" class="form-control" id="studentSearch" placeholder="Search by name or student ID">
                    <div id="studentSearchResults" class="mt-2"></div>
                </div>
                <div class="col-md-6">
                    <label for="selectedStudent" class="form-label">Selected Student</label>
                    <div id="selectedStudentInfo" class="border rounded p-3 bg-light">
                        <p class="text-muted mb-0">No student selected</p>
                    </div>
                </div>
            </div>
            <div class="row mt-3">
                <div class="col-12">
                    <button class="btn btn-primary" onclick="generateStudentBarcode()">
                        <i class="fas fa-barcode me-2"></i>Generate Student Barcode
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Custom Barcode Section -->
<div id="customBarcodeSection" class="barcode-section" style="display: none;">
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="mb-0"><i class="fas fa-edit me-2"></i>Custom Barcode Generator</h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <label for="customText" class="form-label">Barcode Text</label>
                    <input type="text" class="form-control" id="customText" placeholder="Enter text for barcode">
                </div>
                <div class="col-md-6">
                    <label for="customFormat" class="form-label">Format</label>
                    <select class="form-select" id="customFormat">
                        <option value="CODE128">CODE128</option>
                        <option value="CODE39">CODE39</option>
                        <option value="EAN13">EAN13</option>
                        <option value="UPC">UPC</option>
                    </select>
                </div>
            </div>
            <div class="row mt-3">
                <div class="col-12">
                    <button class="btn btn-primary" onclick="generateCustomBarcode()">
                        <i class="fas fa-barcode me-2"></i>Generate Custom Barcode
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Barcode Preview -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0"><i class="fas fa-eye me-2"></i>Barcode Preview</h5>
    </div>
    <div class="card-body text-center">
        <div id="barcodePreview">
            <p class="text-muted">Generate a barcode to see preview</p>
        </div>
        <div class="mt-3" id="barcodeActions" style="display: none;">
            <button class="btn btn-success" onclick="printBarcode()">
                <i class="fas fa-print me-2"></i>Print
            </button>
            <button class="btn btn-info ms-2" onclick="downloadBarcode()">
                <i class="fas fa-download me-2"></i>Download
            </button>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="https://cdn.jsdelivr.net/npm/jsbarcode@3.11.5/dist/JsBarcode.all.min.js"></script>
<script>
// Handle barcode type selection
$('input[name="barcodeType"]').change(function() {
    $('.barcode-section').hide();
    const selectedType = $(this).val();
    $(`#${selectedType}BarcodeSection`).show();
});

// Book search functionality
let selectedBook = null;
$('#bookSearch').on('input', function() {
    const query = $(this).val();
    if (query.length > 2) {
        // Simulate API call for book search
        setTimeout(() => {
            const mockResults = [
                { id: 1, title: 'Introduction to Computer Science', author: 'John Smith', access_no: 'CS001' },
                { id: 2, title: 'Advanced Mathematics', author: 'Jane Doe', access_no: 'MATH001' }
            ];
            
            let resultsHtml = '<div class="list-group">';
            mockResults.forEach(book => {
                resultsHtml += `
                    <a href="#" class="list-group-item list-group-item-action" onclick="selectBook(${book.id}, '${book.title}', '${book.author}', '${book.access_no}')">
                        <strong>${book.title}</strong><br>
                        <small>by ${book.author} - ${book.access_no}</small>
                    </a>
                `;
            });
            resultsHtml += '</div>';
            $('#bookSearchResults').html(resultsHtml);
        }, 300);
    } else {
        $('#bookSearchResults').empty();
    }
});

function selectBook(id, title, author, accessNo) {
    selectedBook = { id, title, author, access_no: accessNo };
    $('#selectedBookInfo').html(`
        <strong>${title}</strong><br>
        <small>by ${author}</small><br>
        <small>Access No: ${accessNo}</small>
    `);
    $('#bookSearchResults').empty();
    $('#bookSearch').val('');
}

// Student search functionality
let selectedStudent = null;
$('#studentSearch').on('input', function() {
    const query = $(this).val();
    if (query.length > 2) {
        // Simulate API call for student search
        setTimeout(() => {
            const mockResults = [
                { id: 1, name: 'Alice Johnson', user_id: 'STU001', department: 'CSE' },
                { id: 2, name: 'Bob Smith', user_id: 'STU002', department: 'IT' }
            ];
            
            let resultsHtml = '<div class="list-group">';
            mockResults.forEach(student => {
                resultsHtml += `
                    <a href="#" class="list-group-item list-group-item-action" onclick="selectStudent(${student.id}, '${student.name}', '${student.user_id}', '${student.department}')">
                        <strong>${student.name}</strong><br>
                        <small>${student.user_id} - ${student.department}</small>
                    </a>
                `;
            });
            resultsHtml += '</div>';
            $('#studentSearchResults').html(resultsHtml);
        }, 300);
    } else {
        $('#studentSearchResults').empty();
    }
});

function selectStudent(id, name, userId, department) {
    selectedStudent = { id, name, user_id: userId, department };
    $('#selectedStudentInfo').html(`
        <strong>${name}</strong><br>
        <small>ID: ${userId}</small><br>
        <small>Department: ${department}</small>
    `);
    $('#studentSearchResults').empty();
    $('#studentSearch').val('');
}

function generateBookBarcode() {
    if (!selectedBook) {
        alert('Please select a book first');
        return;
    }
    
    const format = $('#barcodeFormat').val();
    const text = selectedBook.access_no;
    
    generateBarcode(text, format);
}

function generateStudentBarcode() {
    if (!selectedStudent) {
        alert('Please select a student first');
        return;
    }
    
    const text = selectedStudent.user_id;
    generateBarcode(text, 'CODE128');
}

function generateCustomBarcode() {
    const text = $('#customText').val();
    const format = $('#customFormat').val();
    
    if (!text) {
        alert('Please enter text for the barcode');
        return;
    }
    
    generateBarcode(text, format);
}

function generateBarcode(text, format) {
    const canvas = document.createElement('canvas');
    canvas.id = 'barcodeCanvas';
    
    try {
        JsBarcode(canvas, text, {
            format: format,
            width: 2,
            height: 100,
            displayValue: true
        });
        
        $('#barcodePreview').html('').append(canvas);
        $('#barcodeActions').show();
    } catch (error) {
        alert('Error generating barcode: ' + error.message);
    }
}

function printBarcode() {
    const canvas = document.getElementById('barcodeCanvas');
    if (canvas) {
        const printWindow = window.open('', '_blank');
        printWindow.document.write(`
            <html>
                <head><title>Print Barcode</title></head>
                <body style="text-align: center; margin: 20px;">
                    <img src="${canvas.toDataURL()}" style="max-width: 100%;">
                </body>
            </html>
        `);
        printWindow.document.close();
        printWindow.print();
    }
}

function downloadBarcode() {
    const canvas = document.getElementById('barcodeCanvas');
    if (canvas) {
        const link = document.createElement('a');
        link.download = 'barcode.png';
        link.href = canvas.toDataURL();
        link.click();
    }
}
</script>
{% endblock %}
