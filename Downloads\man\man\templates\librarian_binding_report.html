{% extends "base.html" %}

{% block title %}Binding Report - Librarian{% endblock %}

{% block sidebar %}
{% include 'librarian_sidebar.html' %}
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h2><i class="fas fa-tools me-3 text-primary"></i>Binding Report</h2>
        <p class="text-muted mb-0">Track book binding and maintenance activities</p>
    </div>
    <div>
        <button class="btn btn-success" onclick="exportReport('excel')">
            <i class="fas fa-file-excel me-2"></i>Export Excel
        </button>
        <button class="btn btn-danger ms-2" onclick="exportReport('pdf')">
            <i class="fas fa-file-pdf me-2"></i>Export PDF
        </button>
    </div>
</div>

<!-- Filter Section -->
<div class="card mb-4">
    <div class="card-header">
        <h5 class="mb-0"><i class="fas fa-filter me-2"></i>Filters</h5>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-3">
                <label for="dateFrom" class="form-label">From Date</label>
                <input type="date" class="form-control" id="dateFrom">
            </div>
            <div class="col-md-3">
                <label for="dateTo" class="form-label">To Date</label>
                <input type="date" class="form-control" id="dateTo">
            </div>
            <div class="col-md-3">
                <label for="bindingType" class="form-label">Binding Type</label>
                <select class="form-select" id="bindingType">
                    <option value="">All Types</option>
                    <option value="hardcover">Hardcover</option>
                    <option value="paperback">Paperback</option>
                    <option value="spiral">Spiral</option>
                    <option value="repair">Repair</option>
                </select>
            </div>
            <div class="col-md-3">
                <label for="status" class="form-label">Status</label>
                <select class="form-select" id="status">
                    <option value="">All Status</option>
                    <option value="pending">Pending</option>
                    <option value="in-progress">In Progress</option>
                    <option value="completed">Completed</option>
                    <option value="cancelled">Cancelled</option>
                </select>
            </div>
        </div>
        <div class="row mt-3">
            <div class="col-12">
                <button class="btn btn-primary" onclick="applyFilters()">
                    <i class="fas fa-search me-2"></i>Apply Filters
                </button>
                <button class="btn btn-secondary ms-2" onclick="resetFilters()">
                    <i class="fas fa-undo me-2"></i>Reset
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">Total Jobs</h6>
                        <h3 class="mb-0" id="totalJobs">0</h3>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-tools fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">Completed</h6>
                        <h3 class="mb-0" id="completedJobs">0</h3>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-check-circle fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">In Progress</h6>
                        <h3 class="mb-0" id="inProgressJobs">0</h3>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-clock fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">Avg Cost</h6>
                        <h3 class="mb-0" id="avgCost">₹0</h3>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-rupee-sign fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Binding Jobs Table -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0"><i class="fas fa-table me-2"></i>Binding Jobs</h5>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-striped table-hover" id="bindingTable">
                <thead class="table-dark">
                    <tr>
                        <th>Job ID</th>
                        <th>Book Title</th>
                        <th>Access No.</th>
                        <th>Binding Type</th>
                        <th>Start Date</th>
                        <th>Due Date</th>
                        <th>Cost</th>
                        <th>Status</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody id="bindingTableBody">
                    <!-- Data will be loaded here -->
                </tbody>
            </table>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// Initialize the page
$(document).ready(function() {
    loadBindingData();
    
    // Set default dates (last 30 days)
    const today = new Date();
    const lastMonth = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000);
    
    $('#dateTo').val(today.toISOString().split('T')[0]);
    $('#dateFrom').val(lastMonth.toISOString().split('T')[0]);
});

function loadBindingData() {
    // Show loading state
    $('#bindingTableBody').html('<tr><td colspan="9" class="text-center"><i class="fas fa-spinner fa-spin"></i> Loading...</td></tr>');
    
    // Simulate API call with mock data
    setTimeout(function() {
        const mockData = {
            statistics: {
                totalJobs: 25,
                completedJobs: 18,
                inProgressJobs: 5,
                avgCost: 150
            },
            data: [
                {
                    id: '1',
                    jobId: 'BJ001',
                    bookTitle: 'Introduction to Computer Science',
                    accessNo: 'CS001',
                    bindingType: 'hardcover',
                    startDate: '2024-01-10',
                    dueDate: '2024-01-20',
                    cost: 200,
                    status: 'completed'
                },
                {
                    id: '2',
                    jobId: 'BJ002',
                    bookTitle: 'Advanced Mathematics',
                    accessNo: 'MATH001',
                    bindingType: 'repair',
                    startDate: '2024-01-15',
                    dueDate: '2024-01-25',
                    cost: 100,
                    status: 'in-progress'
                }
            ]
        };
        
        updateStatistics(mockData.statistics);
        populateTable(mockData.data);
    }, 1000);
}

function updateStatistics(stats) {
    $('#totalJobs').text(stats.totalJobs || 0);
    $('#completedJobs').text(stats.completedJobs || 0);
    $('#inProgressJobs').text(stats.inProgressJobs || 0);
    $('#avgCost').text('₹' + (stats.avgCost || 0));
}

function populateTable(data) {
    const tbody = $('#bindingTableBody');
    tbody.empty();
    
    if (data.length === 0) {
        tbody.html('<tr><td colspan="9" class="text-center">No binding jobs found</td></tr>');
        return;
    }
    
    data.forEach(function(job) {
        const row = `
            <tr>
                <td><strong>${job.jobId}</strong></td>
                <td>${job.bookTitle}</td>
                <td>${job.accessNo}</td>
                <td><span class="badge bg-${getBindingTypeColor(job.bindingType)}">${job.bindingType}</span></td>
                <td>${job.startDate}</td>
                <td>${job.dueDate}</td>
                <td>₹${job.cost}</td>
                <td><span class="badge bg-${getStatusColor(job.status)}">${job.status}</span></td>
                <td>
                    <button class="btn btn-sm btn-outline-primary" onclick="viewJob('${job.id}')">
                        <i class="fas fa-eye"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-warning" onclick="editJob('${job.id}')">
                        <i class="fas fa-edit"></i>
                    </button>
                </td>
            </tr>
        `;
        tbody.append(row);
    });
}

function getBindingTypeColor(type) {
    const colors = {
        'hardcover': 'primary',
        'paperback': 'success',
        'spiral': 'info',
        'repair': 'warning'
    };
    return colors[type] || 'secondary';
}

function getStatusColor(status) {
    const colors = {
        'pending': 'secondary',
        'in-progress': 'warning',
        'completed': 'success',
        'cancelled': 'danger'
    };
    return colors[status] || 'secondary';
}

function applyFilters() {
    loadBindingData();
}

function resetFilters() {
    $('#dateFrom').val('');
    $('#dateTo').val('');
    $('#bindingType').val('');
    $('#status').val('');
    loadBindingData();
}

function exportReport(format) {
    alert('Export functionality would be implemented here for format: ' + format);
}

function viewJob(jobId) {
    alert('View job details: ' + jobId);
}

function editJob(jobId) {
    alert('Edit job: ' + jobId);
}
</script>
{% endblock %}
