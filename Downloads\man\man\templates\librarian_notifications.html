{% extends "base.html" %}

{% block title %}Notifications - Librarian{% endblock %}

{% block sidebar %}
{% include 'librarian_sidebar.html' %}
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h2><i class="fas fa-bell me-3 text-primary"></i>Notifications</h2>
        <p class="text-muted mb-0">Manage library notifications and alerts</p>
    </div>
    <div>
        <button class="btn btn-primary" onclick="sendBulkNotification()">
            <i class="fas fa-paper-plane me-2"></i>Send Bulk Notification
        </button>
    </div>
</div>

<!-- Notification Stats -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">Total Sent</h6>
                        <h3 class="mb-0" id="totalSent">245</h3>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-paper-plane fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">Pending</h6>
                        <h3 class="mb-0" id="pendingCount">12</h3>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-clock fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">Delivered</h6>
                        <h3 class="mb-0" id="deliveredCount">198</h3>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-check-circle fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-danger text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">Failed</h6>
                        <h3 class="mb-0" id="failedCount">5</h3>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-exclamation-triangle fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-bolt me-2"></i>Quick Notifications</h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <button class="btn btn-outline-warning" onclick="sendOverdueReminders()">
                        <i class="fas fa-exclamation-triangle me-2"></i>Send Overdue Reminders
                    </button>
                    <button class="btn btn-outline-info" onclick="sendDueSoonReminders()">
                        <i class="fas fa-clock me-2"></i>Send Due Soon Reminders
                    </button>
                    <button class="btn btn-outline-success" onclick="sendNewBookAlerts()">
                        <i class="fas fa-book me-2"></i>Send New Book Alerts
                    </button>
                    <button class="btn btn-outline-primary" onclick="sendMaintenanceNotice()">
                        <i class="fas fa-tools me-2"></i>Send Maintenance Notice
                    </button>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-cog me-2"></i>Notification Settings</h5>
            </div>
            <div class="card-body">
                <div class="form-check mb-2">
                    <input class="form-check-input" type="checkbox" id="autoOverdueReminders" checked>
                    <label class="form-check-label" for="autoOverdueReminders">
                        Auto send overdue reminders
                    </label>
                </div>
                <div class="form-check mb-2">
                    <input class="form-check-input" type="checkbox" id="autoDueSoonReminders" checked>
                    <label class="form-check-label" for="autoDueSoonReminders">
                        Auto send due soon reminders
                    </label>
                </div>
                <div class="form-check mb-2">
                    <input class="form-check-input" type="checkbox" id="emailNotifications" checked>
                    <label class="form-check-label" for="emailNotifications">
                        Enable email notifications
                    </label>
                </div>
                <div class="form-check mb-3">
                    <input class="form-check-input" type="checkbox" id="smsNotifications">
                    <label class="form-check-label" for="smsNotifications">
                        Enable SMS notifications
                    </label>
                </div>
                <button class="btn btn-primary btn-sm" onclick="saveSettings()">
                    <i class="fas fa-save me-2"></i>Save Settings
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Notification History -->
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0"><i class="fas fa-history me-2"></i>Notification History</h5>
        <div>
            <select class="form-select form-select-sm" id="filterType" onchange="filterNotifications()">
                <option value="">All Types</option>
                <option value="overdue">Overdue Reminders</option>
                <option value="due_soon">Due Soon</option>
                <option value="new_book">New Book Alerts</option>
                <option value="maintenance">Maintenance</option>
                <option value="custom">Custom</option>
            </select>
        </div>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-striped table-hover">
                <thead class="table-dark">
                    <tr>
                        <th>Date/Time</th>
                        <th>Type</th>
                        <th>Subject</th>
                        <th>Recipients</th>
                        <th>Status</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody id="notificationHistoryBody">
                    <!-- Notification history will be loaded here -->
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Send Custom Notification Modal -->
<div class="modal fade" id="customNotificationModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Send Custom Notification</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="customNotificationForm">
                    <div class="mb-3">
                        <label for="notificationSubject" class="form-label">Subject</label>
                        <input type="text" class="form-control" id="notificationSubject" required>
                    </div>
                    <div class="mb-3">
                        <label for="notificationMessage" class="form-label">Message</label>
                        <textarea class="form-control" id="notificationMessage" rows="5" required></textarea>
                    </div>
                    <div class="mb-3">
                        <label for="recipientType" class="form-label">Recipients</label>
                        <select class="form-select" id="recipientType" onchange="updateRecipientOptions()">
                            <option value="all">All Students</option>
                            <option value="department">By Department</option>
                            <option value="year">By Year</option>
                            <option value="overdue">Students with Overdue Books</option>
                            <option value="custom">Custom List</option>
                        </select>
                    </div>
                    <div class="mb-3" id="recipientOptions" style="display: none;">
                        <label for="recipientFilter" class="form-label">Filter</label>
                        <select class="form-select" id="recipientFilter">
                            <!-- Options will be populated based on recipient type -->
                        </select>
                    </div>
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="sendEmail" checked>
                            <label class="form-check-label" for="sendEmail">
                                Send via Email
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="sendSMS">
                            <label class="form-check-label" for="sendSMS">
                                Send via SMS
                            </label>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="sendCustomNotification()">
                    <i class="fas fa-paper-plane me-2"></i>Send Notification
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    loadNotificationHistory();
});

function loadNotificationHistory() {
    // Show loading
    $('#notificationHistoryBody').html('<tr><td colspan="6" class="text-center"><i class="fas fa-spinner fa-spin"></i> Loading...</td></tr>');
    
    // Simulate API call
    setTimeout(function() {
        const mockNotifications = [
            {
                id: 1,
                datetime: '2024-01-20 10:30 AM',
                type: 'overdue',
                subject: 'Overdue Book Reminder',
                recipients: 15,
                status: 'delivered'
            },
            {
                id: 2,
                datetime: '2024-01-19 02:00 PM',
                type: 'due_soon',
                subject: 'Books Due Tomorrow',
                recipients: 45,
                status: 'delivered'
            },
            {
                id: 3,
                datetime: '2024-01-18 09:00 AM',
                type: 'new_book',
                subject: 'New Computer Science Books Available',
                recipients: 120,
                status: 'delivered'
            }
        ];
        
        populateNotificationHistory(mockNotifications);
    }, 1000);
}

function populateNotificationHistory(notifications) {
    const tbody = $('#notificationHistoryBody');
    tbody.empty();
    
    if (notifications.length === 0) {
        tbody.html('<tr><td colspan="6" class="text-center text-muted">No notifications found</td></tr>');
        return;
    }
    
    notifications.forEach(function(notification) {
        const typeBadge = getTypeBadge(notification.type);
        const statusBadge = getStatusBadge(notification.status);
        
        const row = `
            <tr>
                <td>${notification.datetime}</td>
                <td>${typeBadge}</td>
                <td>${notification.subject}</td>
                <td>${notification.recipients} recipients</td>
                <td>${statusBadge}</td>
                <td>
                    <button class="btn btn-sm btn-outline-primary" onclick="viewNotification(${notification.id})">
                        <i class="fas fa-eye"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-success" onclick="resendNotification(${notification.id})">
                        <i class="fas fa-redo"></i>
                    </button>
                </td>
            </tr>
        `;
        tbody.append(row);
    });
}

function getTypeBadge(type) {
    const badges = {
        'overdue': '<span class="badge bg-danger">Overdue</span>',
        'due_soon': '<span class="badge bg-warning">Due Soon</span>',
        'new_book': '<span class="badge bg-success">New Book</span>',
        'maintenance': '<span class="badge bg-info">Maintenance</span>',
        'custom': '<span class="badge bg-primary">Custom</span>'
    };
    return badges[type] || '<span class="badge bg-secondary">Unknown</span>';
}

function getStatusBadge(status) {
    const badges = {
        'delivered': '<span class="badge bg-success">Delivered</span>',
        'pending': '<span class="badge bg-warning">Pending</span>',
        'failed': '<span class="badge bg-danger">Failed</span>'
    };
    return badges[status] || '<span class="badge bg-secondary">Unknown</span>';
}

function sendOverdueReminders() {
    if (confirm('Send overdue reminders to all students with overdue books?')) {
        alert('Overdue reminders sent to 15 students');
        loadNotificationHistory();
    }
}

function sendDueSoonReminders() {
    if (confirm('Send due soon reminders to students with books due in 2 days?')) {
        alert('Due soon reminders sent to 45 students');
        loadNotificationHistory();
    }
}

function sendNewBookAlerts() {
    if (confirm('Send new book alerts to all students?')) {
        alert('New book alerts sent to 120 students');
        loadNotificationHistory();
    }
}

function sendMaintenanceNotice() {
    if (confirm('Send maintenance notice to all users?')) {
        alert('Maintenance notice sent to all users');
        loadNotificationHistory();
    }
}

function sendBulkNotification() {
    $('#customNotificationModal').modal('show');
}

function updateRecipientOptions() {
    const recipientType = $('#recipientType').val();
    const optionsDiv = $('#recipientOptions');
    const filterSelect = $('#recipientFilter');
    
    filterSelect.empty();
    
    if (recipientType === 'department') {
        optionsDiv.show();
        filterSelect.append('<option value="CSE">Computer Science</option>');
        filterSelect.append('<option value="ECE">Electronics</option>');
        filterSelect.append('<option value="MECH">Mechanical</option>');
        filterSelect.append('<option value="CIVIL">Civil</option>');
    } else if (recipientType === 'year') {
        optionsDiv.show();
        filterSelect.append('<option value="1">First Year</option>');
        filterSelect.append('<option value="2">Second Year</option>');
        filterSelect.append('<option value="3">Third Year</option>');
        filterSelect.append('<option value="4">Fourth Year</option>');
    } else {
        optionsDiv.hide();
    }
}

function sendCustomNotification() {
    const subject = $('#notificationSubject').val().trim();
    const message = $('#notificationMessage').val().trim();
    const recipientType = $('#recipientType').val();
    
    if (!subject || !message) {
        alert('Please fill in all required fields');
        return;
    }
    
    // Simulate sending notification
    alert(`Custom notification "${subject}" sent successfully!`);
    $('#customNotificationModal').modal('hide');
    $('#customNotificationForm')[0].reset();
    loadNotificationHistory();
}

function filterNotifications() {
    const filterType = $('#filterType').val();
    // In a real application, this would filter the notifications
    loadNotificationHistory();
}

function viewNotification(id) {
    alert('View notification details: ' + id);
}

function resendNotification(id) {
    if (confirm('Resend this notification?')) {
        alert('Notification resent successfully');
        loadNotificationHistory();
    }
}

function saveSettings() {
    alert('Notification settings saved successfully');
}
</script>
{% endblock %}
